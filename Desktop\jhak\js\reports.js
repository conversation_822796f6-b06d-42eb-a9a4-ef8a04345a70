// تكنوفلاش - التقارير والإحصائيات
class ReportsManager {
    constructor() {
        this.init();
    }

    init() {}

    load() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="reports-page fade-in">
                <div class="page-header">
                    <div class="page-title">
                        <h1><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</h1>
                        <p>تقارير شاملة للمبيعات والمخزون والأرباح</p>
                    </div>
                </div>

                <div class="reports-grid">
                    <div class="report-card" onclick="reportsManager.showSalesReport()">
                        <div class="report-icon sales">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="report-info">
                            <h3>تقرير المبيعات</h3>
                            <p>تقرير مفصل للمبيعات حسب الفترة</p>
                        </div>
                    </div>

                    <div class="report-card" onclick="reportsManager.showInventoryReport()">
                        <div class="report-icon inventory">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="report-info">
                            <h3>تقرير المخزون</h3>
                            <p>حالة المخزون والمنتجات</p>
                        </div>
                    </div>

                    <div class="report-card" onclick="reportsManager.showCustomersReport()">
                        <div class="report-icon customers">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="report-info">
                            <h3>تقرير العملاء</h3>
                            <p>إحصائيات العملاء والديون</p>
                        </div>
                    </div>

                    <div class="report-card" onclick="reportsManager.showProfitReport()">
                        <div class="report-icon profit">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="report-info">
                            <h3>تقرير الأرباح</h3>
                            <p>تحليل الأرباح والخسائر</p>
                        </div>
                    </div>
                </div>

                <div class="quick-stats">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">إحصائيات سريعة</h3>
                        </div>
                        <div class="card-body">
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <h4>مبيعات اليوم</h4>
                                    <div class="stat-value" id="todaySales">٠ ريال</div>
                                </div>
                                <div class="stat-item">
                                    <h4>مبيعات الشهر</h4>
                                    <div class="stat-value" id="monthSales">٠ ريال</div>
                                </div>
                                <div class="stat-item">
                                    <h4>عدد الفواتير</h4>
                                    <div class="stat-value" id="totalInvoices">٠</div>
                                </div>
                                <div class="stat-item">
                                    <h4>متوسط الفاتورة</h4>
                                    <div class="stat-value" id="avgInvoice">٠ ريال</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.loadQuickStats();
    }

    loadQuickStats() {
        const sales = db.getSales();
        const today = new Date();
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        
        // مبيعات اليوم
        const todaySales = sales.filter(sale => {
            const saleDate = new Date(sale.createdAt);
            return saleDate.toDateString() === today.toDateString();
        }).reduce((sum, sale) => sum + sale.total, 0);
        
        // مبيعات الشهر
        const monthSales = sales.filter(sale => {
            const saleDate = new Date(sale.createdAt);
            return saleDate >= startOfMonth;
        }).reduce((sum, sale) => sum + sale.total, 0);
        
        // متوسط الفاتورة
        const avgInvoice = sales.length > 0 ? monthSales / sales.length : 0;
        
        document.getElementById('todaySales').textContent = app.formatCurrency(todaySales);
        document.getElementById('monthSales').textContent = app.formatCurrency(monthSales);
        document.getElementById('totalInvoices').textContent = db.toArabicNumbers(sales.length);
        document.getElementById('avgInvoice').textContent = app.formatCurrency(avgInvoice);
    }

    showSalesReport() {
        app.showAlert('تقرير المبيعات - قيد التطوير', 'info');
    }

    showInventoryReport() {
        app.showAlert('تقرير المخزون - قيد التطوير', 'info');
    }

    showCustomersReport() {
        app.showAlert('تقرير العملاء - قيد التطوير', 'info');
    }

    showProfitReport() {
        app.showAlert('تقرير الأرباح - قيد التطوير', 'info');
    }
}

const reportsManager = new ReportsManager();

function loadReports() {
    if (typeof app !== 'undefined') {
        app.currentPage = 'reports';
        app.updateActiveNavItem('reports');
    }
    reportsManager.load();
}
