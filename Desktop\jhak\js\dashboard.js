// تكنوفلاش - لوحة المعلومات الرئيسية

// وظائف مساعدة للتنسيق
function formatCurrencyFallback(amount) {
    if (typeof app !== 'undefined' && app.formatCurrency) {
        return app.formatCurrency(amount);
    }
    return parseFloat(amount).toFixed(2) + ' ريال';
}

function formatDateFallback(date) {
    if (typeof app !== 'undefined' && app.formatDate) {
        return app.formatDate(date);
    }
    return new Date(date).toLocaleDateString('ar-SA');
}

function formatTimeFallback(date) {
    if (typeof app !== 'undefined' && app.formatTime) {
        return app.formatTime(date);
    }
    return new Date(date).toLocaleTimeString('ar-SA');
}

function toArabicNumbersFallback(num) {
    if (typeof db !== 'undefined' && db.toArabicNumbers) {
        return db.toArabicNumbers(num);
    }
    return num.toString();
}

class Dashboard {
    constructor() {
        this.refreshInterval = null;
        this.charts = {};
        this.init();
    }

    init() {
        this.setupAutoRefresh();
    }

    // تحميل لوحة المعلومات
    load() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getHTML();
        
        // تحميل البيانات
        this.loadData();
        
        // إعداد الأحداث
        this.setupEvents();
        
        // بدء التحديث التلقائي
        this.startAutoRefresh();
    }

    // الحصول على HTML لوحة المعلومات
    getHTML() {
        return `
            <div class="dashboard fade-in">
                <!-- رأس لوحة المعلومات -->
                <div class="dashboard-header">
                    <div class="welcome-section">
                        <h1>مرحباً بك في تكنوفلاش</h1>
                        <p>نظام إدارة نقاط البيع العربي</p>
                        <div class="current-date-time">
                            <span class="current-date">${formatDateFallback(new Date())}</span>
                            <span class="current-time">${formatTimeFallback(new Date())}</span>
                        </div>
                    </div>
                    <div class="quick-actions">
                        <button class="btn btn-primary" onclick="showSales()">
                            <i class="fas fa-plus"></i>
                            بيع جديد
                        </button>
                        <button class="btn btn-secondary" onclick="showProducts()">
                            <i class="fas fa-box"></i>
                            إضافة منتج
                        </button>
                        <button class="btn btn-success" onclick="showCustomers()">
                            <i class="fas fa-user-plus"></i>
                            عميل جديد
                        </button>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات السريعة -->
                <div class="stats-grid">
                    <div class="stat-card sales-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalSales">٠</h3>
                            <p>إجمالي المبيعات</p>
                            <small class="stat-change positive" id="salesChange">+٠%</small>
                        </div>
                    </div>

                    <div class="stat-card products-card">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalProducts">٠</h3>
                            <p>عدد المنتجات</p>
                            <small class="stat-info" id="lowStockCount">٠ منخفض المخزون</small>
                        </div>
                    </div>

                    <div class="stat-card customers-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalCustomers">٠</h3>
                            <p>عدد العملاء</p>
                            <small class="stat-info" id="debtorsCount">٠ مدين</small>
                        </div>
                    </div>

                    <div class="stat-card profit-card">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalProfit">٠</h3>
                            <p>صافي الربح</p>
                            <small class="stat-change positive" id="profitChange">+٠%</small>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية والتقارير -->
                <div class="dashboard-charts">
                    <div class="chart-container">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-chart-bar"></i>
                                    مبيعات آخر ٧ أيام
                                </h3>
                                <div class="chart-controls">
                                    <select id="salesPeriod" onchange="dashboard.updateSalesChart()">
                                        <option value="7">آخر ٧ أيام</option>
                                        <option value="30">آخر ٣٠ يوم</option>
                                        <option value="90">آخر ٩٠ يوم</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="salesChart" class="chart"></div>
                            </div>
                        </div>
                    </div>

                    <div class="chart-container">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-pie-chart"></i>
                                    أفضل المنتجات مبيعاً
                                </h3>
                            </div>
                            <div class="card-body">
                                <div id="topProductsChart" class="chart"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التنبيهات والإشعارات -->
                <div class="dashboard-alerts">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-bell"></i>
                                التنبيهات والإشعارات
                            </h3>
                        </div>
                        <div class="card-body">
                            <div id="alertsList" class="alerts-list">
                                <!-- سيتم تحميل التنبيهات هنا -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الأنشطة الأخيرة -->
                <div class="recent-activities">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-history"></i>
                                الأنشطة الأخيرة
                            </h3>
                            <button class="btn btn-sm btn-secondary" onclick="dashboard.refreshActivities()">
                                <i class="fas fa-refresh"></i>
                                تحديث
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="activitiesList" class="activities-list">
                                <!-- سيتم تحميل الأنشطة هنا -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // تحميل البيانات
    loadData() {
        this.loadStats();
        this.loadCharts();
        this.loadAlerts();
        this.loadRecentActivities();
    }

    // تحميل الإحصائيات
    loadStats() {
        // إجمالي المبيعات
        const sales = this.getSalesData();
        const totalSales = sales.reduce((sum, sale) => sum + sale.total, 0);
        const totalSalesEl = document.getElementById('totalSales');
        if (totalSalesEl) totalSalesEl.textContent = formatCurrencyFallback(totalSales);

        // عدد المنتجات
        const products = db.getProducts();
        const totalProductsEl = document.getElementById('totalProducts');
        if (totalProductsEl) totalProductsEl.textContent = toArabicNumbersFallback(products.length);

        // المنتجات منخفضة المخزون
        const lowStockProducts = db.getLowStockProducts();
        const lowStockCountEl = document.getElementById('lowStockCount');
        if (lowStockCountEl) lowStockCountEl.textContent =
            `${toArabicNumbersFallback(lowStockProducts.length)} منخفض المخزون`;

        // عدد العملاء
        const customers = db.getCustomers().filter(c => !c.isGuest);
        const totalCustomersEl = document.getElementById('totalCustomers');
        if (totalCustomersEl) totalCustomersEl.textContent = toArabicNumbersFallback(customers.length);

        // العملاء المدينين
        const debtors = customers.filter(c => c.balance < 0);
        const debtorsCountEl = document.getElementById('debtorsCount');
        if (debtorsCountEl) debtorsCountEl.textContent =
            `${toArabicNumbersFallback(debtors.length)} مدين`;

        // صافي الربح (تقدير بسيط)
        const totalProfit = totalSales * 0.3; // افتراض هامش ربح 30%
        const totalProfitEl = document.getElementById('totalProfit');
        if (totalProfitEl) totalProfitEl.textContent = formatCurrencyFallback(totalProfit);

        // حساب التغيير في المبيعات
        this.calculateSalesChange();
    }

    // حساب التغيير في المبيعات
    calculateSalesChange() {
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        const todaySales = this.getSalesForDate(today);
        const yesterdaySales = this.getSalesForDate(yesterday);

        const todayTotal = todaySales.reduce((sum, sale) => sum + sale.total, 0);
        const yesterdayTotal = yesterdaySales.reduce((sum, sale) => sum + sale.total, 0);

        let change = 0;
        if (yesterdayTotal > 0) {
            change = ((todayTotal - yesterdayTotal) / yesterdayTotal) * 100;
        }

        const changeElement = document.getElementById('salesChange');
        if (changeElement) {
            const changeText = change >= 0 ? `+${toArabicNumbersFallback(Math.abs(change).toFixed(1))}%` :
                                            `-${toArabicNumbersFallback(Math.abs(change).toFixed(1))}%`;

            changeElement.textContent = changeText;
            changeElement.className = `stat-change ${change >= 0 ? 'positive' : 'negative'}`;
        }
    }

    // تحميل الرسوم البيانية
    loadCharts() {
        this.loadSalesChart();
        this.loadTopProductsChart();
    }

    // تحميل رسم المبيعات
    loadSalesChart() {
        const period = parseInt(document.getElementById('salesPeriod')?.value || 7);
        const salesData = this.getSalesChartData(period);
        
        appCore.createSimpleChart(salesData, 'salesChart', 'bar');
    }

    // تحديث رسم المبيعات
    updateSalesChart() {
        this.loadSalesChart();
    }

    // تحميل رسم أفضل المنتجات
    loadTopProductsChart() {
        const topProducts = this.getTopProducts(5);
        appCore.createSimpleChart(topProducts, 'topProductsChart', 'bar');
    }

    // تحميل التنبيهات
    loadAlerts() {
        const alerts = [];

        // تنبيهات المخزون المنخفض
        const lowStockProducts = db.getLowStockProducts();
        if (lowStockProducts.length > 0) {
            alerts.push({
                type: 'warning',
                icon: 'fas fa-exclamation-triangle',
                title: 'مخزون منخفض',
                message: `${db.toArabicNumbers(lowStockProducts.length)} منتج منخفض المخزون`,
                action: 'showProducts()',
                actionText: 'عرض المنتجات'
            });
        }

        // تنبيهات الديون
        const customers = db.getCustomers();
        const debtors = customers.filter(c => c.balance < -1000); // ديون أكثر من 1000
        if (debtors.length > 0) {
            alerts.push({
                type: 'danger',
                icon: 'fas fa-money-bill-wave',
                title: 'ديون مرتفعة',
                message: `${db.toArabicNumbers(debtors.length)} عميل لديه ديون مرتفعة`,
                action: 'showDebts()',
                actionText: 'إدارة الديون'
            });
        }

        // عرض التنبيهات
        this.displayAlerts(alerts);
    }

    // عرض التنبيهات
    displayAlerts(alerts) {
        const alertsList = document.getElementById('alertsList');
        
        if (alerts.length === 0) {
            alertsList.innerHTML = '<div class="no-alerts">لا توجد تنبيهات جديدة</div>';
            return;
        }

        let html = '';
        alerts.forEach(alert => {
            html += `
                <div class="alert alert-${alert.type}">
                    <div class="alert-icon">
                        <i class="${alert.icon}"></i>
                    </div>
                    <div class="alert-content">
                        <h4>${alert.title}</h4>
                        <p>${alert.message}</p>
                    </div>
                    <div class="alert-action">
                        <button class="btn btn-sm btn-primary" onclick="${alert.action}">
                            ${alert.actionText}
                        </button>
                    </div>
                </div>
            `;
        });

        alertsList.innerHTML = html;
    }

    // تحميل الأنشطة الأخيرة
    loadRecentActivities() {
        const activities = this.getRecentActivities(10);
        this.displayActivities(activities);
    }

    // عرض الأنشطة
    displayActivities(activities) {
        const activitiesList = document.getElementById('activitiesList');
        
        if (activities.length === 0) {
            activitiesList.innerHTML = '<div class="no-activities">لا توجد أنشطة حديثة</div>';
            return;
        }

        let html = '';
        activities.forEach(activity => {
            html += `
                <div class="activity-item">
                    <div class="activity-icon ${activity.type}">
                        <i class="${activity.icon}"></i>
                    </div>
                    <div class="activity-content">
                        <p>${activity.description}</p>
                        <small class="activity-time">${app.formatTime(activity.timestamp)}</small>
                    </div>
                </div>
            `;
        });

        activitiesList.innerHTML = html;
    }

    // تحديث الأنشطة
    refreshActivities() {
        this.loadRecentActivities();
        app.showAlert('تم تحديث الأنشطة', 'success');
    }

    // إعداد الأحداث
    setupEvents() {
        // تحديث الوقت كل دقيقة
        setInterval(() => {
            const timeElement = document.querySelector('.current-time');
            if (timeElement) {
                timeElement.textContent = app.formatTime(new Date());
            }
        }, 60000);
    }

    // إعداد التحديث التلقائي
    setupAutoRefresh() {
        // تحديث البيانات كل 5 دقائق
        this.refreshInterval = setInterval(() => {
            if (app.currentPage === 'dashboard') {
                this.loadData();
            }
        }, 300000);
    }

    // بدء التحديث التلقائي
    startAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        this.setupAutoRefresh();
    }

    // إيقاف التحديث التلقائي
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    // الحصول على بيانات المبيعات
    getSalesData() {
        // محاكاة بيانات المبيعات - في التطبيق الحقيقي ستأتي من قاعدة البيانات
        return [
            { id: 1, total: 1500, date: new Date().toISOString() },
            { id: 2, total: 2300, date: new Date(Date.now() - 86400000).toISOString() },
            { id: 3, total: 1800, date: new Date(Date.now() - 172800000).toISOString() }
        ];
    }

    // الحصول على مبيعات تاريخ محدد
    getSalesForDate(date) {
        const dateStr = date.toDateString();
        return this.getSalesData().filter(sale => 
            new Date(sale.date).toDateString() === dateStr
        );
    }

    // الحصول على بيانات رسم المبيعات
    getSalesChartData(days) {
        const data = [];
        const today = new Date();
        
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            
            const sales = this.getSalesForDate(date);
            const total = sales.reduce((sum, sale) => sum + sale.total, 0);
            
            data.push({
                label: app.formatDate(date),
                value: total
            });
        }
        
        return data;
    }

    // الحصول على أفضل المنتجات
    getTopProducts(limit) {
        const products = db.getProducts();
        // محاكاة بيانات المبيعات للمنتجات
        const productsWithSales = products.map(product => ({
            ...product,
            salesCount: Math.floor(Math.random() * 100) + 1
        }));
        
        return productsWithSales
            .sort((a, b) => b.salesCount - a.salesCount)
            .slice(0, limit)
            .map(product => ({
                label: product.name,
                value: product.salesCount
            }));
    }

    // الحصول على الأنشطة الأخيرة
    getRecentActivities(limit) {
        // محاكاة الأنشطة الأخيرة
        const activities = [
            {
                type: 'sale',
                icon: 'fas fa-shopping-cart',
                description: 'تم إنجاز عملية بيع بقيمة ٢٥٠ ريال',
                timestamp: new Date().toISOString()
            },
            {
                type: 'product',
                icon: 'fas fa-box',
                description: 'تم إضافة منتج جديد: لابتوب ديل',
                timestamp: new Date(Date.now() - 3600000).toISOString()
            },
            {
                type: 'customer',
                icon: 'fas fa-user',
                description: 'تم إضافة عميل جديد: أحمد محمد',
                timestamp: new Date(Date.now() - 7200000).toISOString()
            }
        ];
        
        return activities.slice(0, limit);
    }
}

// إنشاء مثيل لوحة المعلومات
const dashboard = new Dashboard();

// وظيفة تحميل لوحة المعلومات
function loadDashboard() {
    if (typeof app !== 'undefined') {
        app.currentPage = 'dashboard';
        app.updateActiveNavItem('dashboard');
    }
    dashboard.load();
}
