// تكنوفلاش - نظام المشتريات
class PurchasesManager {
    constructor() {
        this.init();
    }

    init() {}

    load() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="purchases-page fade-in">
                <div class="page-header">
                    <div class="page-title">
                        <h1><i class="fas fa-shopping-bag"></i> إدارة المشتريات</h1>
                        <p>تسجيل وإدارة فواتير المشتريات</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            إضافة فاتورة شراء
                        </button>
                    </div>
                </div>
                <div class="card">
                    <div class="card-body">
                        <div class="empty-state">
                            <i class="fas fa-shopping-bag"></i>
                            <h3>نظام المشتريات</h3>
                            <p>سيتم تطوير هذا القسم قريباً</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}

const purchasesManager = new PurchasesManager();

function loadPurchases() {
    if (typeof app !== 'undefined') {
        app.currentPage = 'purchases';
        app.updateActiveNavItem('purchases');
    }
    purchasesManager.load();
}
