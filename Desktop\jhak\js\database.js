// تكنوفلاش - إدارة قاعدة البيانات المحلية
class TechnoFlashDB {
    constructor() {
        this.dbName = 'technoflash_pos';
        this.version = '1.0.0';
        this.init();
    }

    // تهيئة قاعدة البيانات
    init() {
        // إنشاء الجداول الافتراضية إذا لم تكن موجودة
        this.initTables();
        
        // إنشاء البيانات الافتراضية
        this.initDefaultData();
        
        // تشغيل الحفظ التلقائي
        this.startAutoSave();
    }

    // إنشاء الجداول
    initTables() {
        const tables = [
            'settings',
            'users',
            'categories',
            'products',
            'customers',
            'suppliers',
            'sales',
            'sale_items',
            'purchases',
            'purchase_items',
            'payments',
            'debts',
            'reports'
        ];

        tables.forEach(table => {
            if (!localStorage.getItem(`${this.dbName}_${table}`)) {
                localStorage.setItem(`${this.dbName}_${table}`, JSON.stringify([]));
            }
        });
    }

    // إنشاء البيانات الافتراضية
    initDefaultData() {
        // الإعدادات الافتراضية
        if (!this.getSettings()) {
            const defaultSettings = {
                companyName: 'شركتي',
                companyAddress: 'العنوان',
                companyPhone: '٠١٢٣٤٥٦٧٨٩',
                companyEmail: '<EMAIL>',
                taxRate: 15,
                currency: 'ريال',
                password: this.hashPassword('123'),
                theme: 'light',
                language: 'ar',
                printSettings: {
                    showLogo: true,
                    showCompanyInfo: true,
                    showTax: true,
                    paperSize: 'A4'
                },
                notifications: {
                    lowStock: true,
                    overdueDebts: true,
                    dailySummary: true
                }
            };
            this.saveSettings(defaultSettings);
        }

        // العميل الافتراضي (ضيف)
        if (this.getCustomers().length === 0) {
            const guestCustomer = {
                id: this.generateId(),
                name: 'ضيف',
                phone: '',
                email: '',
                address: '',
                balance: 0,
                isGuest: true,
                createdAt: new Date().toISOString()
            };
            this.addCustomer(guestCustomer);
        }

        // فئات افتراضية
        if (this.getCategories().length === 0) {
            const defaultCategories = [
                { id: this.generateId(), name: 'عام', description: 'فئة عامة', createdAt: new Date().toISOString() },
                { id: this.generateId(), name: 'إلكترونيات', description: 'الأجهزة الإلكترونية', createdAt: new Date().toISOString() },
                { id: this.generateId(), name: 'ملابس', description: 'الملابس والأزياء', createdAt: new Date().toISOString() }
            ];
            defaultCategories.forEach(category => this.addCategory(category));
        }
    }

    // توليد معرف فريد
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // تشفير كلمة المرور
    hashPassword(password) {
        // تشفير بسيط - في التطبيق الحقيقي يجب استخدام تشفير أقوى
        return btoa(password + 'technoflash_salt');
    }

    // التحقق من كلمة المرور
    verifyPassword(password, hash) {
        return this.hashPassword(password) === hash;
    }

    // تحويل الأرقام إلى عربية هندية
    toArabicNumbers(num) {
        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        return num.toString().replace(/[0-9]/g, (w) => arabicNumbers[+w]);
    }

    // تحويل الأرقام من عربية هندية إلى إنجليزية
    fromArabicNumbers(str) {
        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        let result = str.toString();
        arabicNumbers.forEach((arabic, index) => {
            result = result.replace(new RegExp(arabic, 'g'), index.toString());
        });
        return result;
    }

    // تنسيق العملة
    formatCurrency(amount) {
        const settings = this.getSettings();
        const currency = settings?.currency || 'ريال';
        return `${this.toArabicNumbers(parseFloat(amount).toFixed(2))} ${currency}`;
    }

    // تنسيق التاريخ
    formatDate(date) {
        const d = new Date(date);
        const day = this.toArabicNumbers(d.getDate().toString().padStart(2, '0'));
        const month = this.toArabicNumbers((d.getMonth() + 1).toString().padStart(2, '0'));
        const year = this.toArabicNumbers(d.getFullYear());
        return `${day}/${month}/${year}`;
    }

    // تنسيق الوقت
    formatTime(date) {
        const d = new Date(date);
        const hours = this.toArabicNumbers(d.getHours().toString().padStart(2, '0'));
        const minutes = this.toArabicNumbers(d.getMinutes().toString().padStart(2, '0'));
        return `${hours}:${minutes}`;
    }

    // === وظائف الإعدادات ===
    getSettings() {
        const settings = localStorage.getItem(`${this.dbName}_settings`);
        return settings ? JSON.parse(settings) : null;
    }

    saveSettings(settings) {
        localStorage.setItem(`${this.dbName}_settings`, JSON.stringify(settings));
        this.triggerEvent('settingsUpdated', settings);
    }

    // === وظائف الفئات ===
    getCategories() {
        const categories = localStorage.getItem(`${this.dbName}_categories`);
        return categories ? JSON.parse(categories) : [];
    }

    addCategory(category) {
        const categories = this.getCategories();
        category.id = category.id || this.generateId();
        category.createdAt = category.createdAt || new Date().toISOString();
        categories.push(category);
        localStorage.setItem(`${this.dbName}_categories`, JSON.stringify(categories));
        this.triggerEvent('categoryAdded', category);
        return category;
    }

    updateCategory(id, updates) {
        const categories = this.getCategories();
        const index = categories.findIndex(cat => cat.id === id);
        if (index !== -1) {
            categories[index] = { ...categories[index], ...updates, updatedAt: new Date().toISOString() };
            localStorage.setItem(`${this.dbName}_categories`, JSON.stringify(categories));
            this.triggerEvent('categoryUpdated', categories[index]);
            return categories[index];
        }
        return null;
    }

    deleteCategory(id) {
        const categories = this.getCategories();
        const index = categories.findIndex(cat => cat.id === id);
        if (index !== -1) {
            const deleted = categories.splice(index, 1)[0];
            localStorage.setItem(`${this.dbName}_categories`, JSON.stringify(categories));
            this.triggerEvent('categoryDeleted', deleted);
            return deleted;
        }
        return null;
    }

    getCategoryById(id) {
        return this.getCategories().find(cat => cat.id === id);
    }

    // === وظائف المنتجات ===
    getProducts() {
        const products = localStorage.getItem(`${this.dbName}_products`);
        return products ? JSON.parse(products) : [];
    }

    addProduct(product) {
        const products = this.getProducts();
        product.id = product.id || this.generateId();
        product.createdAt = product.createdAt || new Date().toISOString();
        products.push(product);
        localStorage.setItem(`${this.dbName}_products`, JSON.stringify(products));
        this.triggerEvent('productAdded', product);
        return product;
    }

    updateProduct(id, updates) {
        const products = this.getProducts();
        const index = products.findIndex(prod => prod.id === id);
        if (index !== -1) {
            products[index] = { ...products[index], ...updates, updatedAt: new Date().toISOString() };
            localStorage.setItem(`${this.dbName}_products`, JSON.stringify(products));
            this.triggerEvent('productUpdated', products[index]);
            return products[index];
        }
        return null;
    }

    deleteProduct(id) {
        const products = this.getProducts();
        const index = products.findIndex(prod => prod.id === id);
        if (index !== -1) {
            const deleted = products.splice(index, 1)[0];
            localStorage.setItem(`${this.dbName}_products`, JSON.stringify(products));
            this.triggerEvent('productDeleted', deleted);
            return deleted;
        }
        return null;
    }

    getProductById(id) {
        return this.getProducts().find(prod => prod.id === id);
    }

    // البحث في المنتجات
    searchProducts(query) {
        const products = this.getProducts();
        const searchTerm = query.toLowerCase();
        return products.filter(product => 
            product.name.toLowerCase().includes(searchTerm) ||
            product.description.toLowerCase().includes(searchTerm) ||
            product.barcode === query
        );
    }

    // المنتجات منخفضة المخزون
    getLowStockProducts(threshold = 10) {
        return this.getProducts().filter(product => product.quantity <= threshold);
    }

    // === وظائف العملاء ===
    getCustomers() {
        const customers = localStorage.getItem(`${this.dbName}_customers`);
        return customers ? JSON.parse(customers) : [];
    }

    addCustomer(customer) {
        const customers = this.getCustomers();
        customer.id = customer.id || this.generateId();
        customer.createdAt = customer.createdAt || new Date().toISOString();
        customer.balance = customer.balance || 0;
        customers.push(customer);
        localStorage.setItem(`${this.dbName}_customers`, JSON.stringify(customers));
        this.triggerEvent('customerAdded', customer);
        return customer;
    }

    updateCustomer(id, updates) {
        const customers = this.getCustomers();
        const index = customers.findIndex(cust => cust.id === id);
        if (index !== -1) {
            customers[index] = { ...customers[index], ...updates, updatedAt: new Date().toISOString() };
            localStorage.setItem(`${this.dbName}_customers`, JSON.stringify(customers));
            this.triggerEvent('customerUpdated', customers[index]);
            return customers[index];
        }
        return null;
    }

    deleteCustomer(id) {
        const customers = this.getCustomers();
        const index = customers.findIndex(cust => cust.id === id);
        if (index !== -1 && !customers[index].isGuest) {
            const deleted = customers.splice(index, 1)[0];
            localStorage.setItem(`${this.dbName}_customers`, JSON.stringify(customers));
            this.triggerEvent('customerDeleted', deleted);
            return deleted;
        }
        return null;
    }

    getCustomerById(id) {
        return this.getCustomers().find(cust => cust.id === id);
    }

    getGuestCustomer() {
        return this.getCustomers().find(cust => cust.isGuest);
    }

    // === وظائف الموردين ===
    getSuppliers() {
        const suppliers = localStorage.getItem(`${this.dbName}_suppliers`);
        return suppliers ? JSON.parse(suppliers) : [];
    }

    addSupplier(supplier) {
        const suppliers = this.getSuppliers();
        supplier.id = supplier.id || this.generateId();
        supplier.createdAt = supplier.createdAt || new Date().toISOString();
        supplier.balance = supplier.balance || 0;
        suppliers.push(supplier);
        localStorage.setItem(`${this.dbName}_suppliers`, JSON.stringify(suppliers));
        this.triggerEvent('supplierAdded', supplier);
        return supplier;
    }

    updateSupplier(id, updates) {
        const suppliers = this.getSuppliers();
        const index = suppliers.findIndex(supp => supp.id === id);
        if (index !== -1) {
            suppliers[index] = { ...suppliers[index], ...updates, updatedAt: new Date().toISOString() };
            localStorage.setItem(`${this.dbName}_suppliers`, JSON.stringify(suppliers));
            this.triggerEvent('supplierUpdated', suppliers[index]);
            return suppliers[index];
        }
        return null;
    }

    deleteSupplier(id) {
        const suppliers = this.getSuppliers();
        const index = suppliers.findIndex(supp => supp.id === id);
        if (index !== -1) {
            const deleted = suppliers.splice(index, 1)[0];
            localStorage.setItem(`${this.dbName}_suppliers`, JSON.stringify(suppliers));
            this.triggerEvent('supplierDeleted', deleted);
            return deleted;
        }
        return null;
    }

    getSupplierById(id) {
        return this.getSuppliers().find(supp => supp.id === id);
    }

    // === وظائف المبيعات ===
    getSales() {
        const sales = localStorage.getItem(`${this.dbName}_sales`);
        return sales ? JSON.parse(sales) : [];
    }

    addSale(sale) {
        const sales = this.getSales();
        sale.id = sale.id || this.generateId();
        sale.createdAt = sale.createdAt || new Date().toISOString();
        sales.push(sale);
        localStorage.setItem(`${this.dbName}_sales`, JSON.stringify(sales));
        this.triggerEvent('saleAdded', sale);
        return sale;
    }

    updateSale(id, updates) {
        const sales = this.getSales();
        const index = sales.findIndex(sale => sale.id === id);
        if (index !== -1) {
            sales[index] = { ...sales[index], ...updates, updatedAt: new Date().toISOString() };
            localStorage.setItem(`${this.dbName}_sales`, JSON.stringify(sales));
            this.triggerEvent('saleUpdated', sales[index]);
            return sales[index];
        }
        return null;
    }

    deleteSale(id) {
        const sales = this.getSales();
        const index = sales.findIndex(sale => sale.id === id);
        if (index !== -1) {
            const deleted = sales.splice(index, 1)[0];
            localStorage.setItem(`${this.dbName}_sales`, JSON.stringify(sales));
            this.triggerEvent('saleDeleted', deleted);
            return deleted;
        }
        return null;
    }

    getSaleById(id) {
        return this.getSales().find(sale => sale.id === id);
    }

    // === وظائف المشتريات ===
    getPurchases() {
        const purchases = localStorage.getItem(`${this.dbName}_purchases`);
        return purchases ? JSON.parse(purchases) : [];
    }

    addPurchase(purchase) {
        const purchases = this.getPurchases();
        purchase.id = purchase.id || this.generateId();
        purchase.createdAt = purchase.createdAt || new Date().toISOString();
        purchases.push(purchase);
        localStorage.setItem(`${this.dbName}_purchases`, JSON.stringify(purchases));
        this.triggerEvent('purchaseAdded', purchase);
        return purchase;
    }

    updatePurchase(id, updates) {
        const purchases = this.getPurchases();
        const index = purchases.findIndex(purchase => purchase.id === id);
        if (index !== -1) {
            purchases[index] = { ...purchases[index], ...updates, updatedAt: new Date().toISOString() };
            localStorage.setItem(`${this.dbName}_purchases`, JSON.stringify(purchases));
            this.triggerEvent('purchaseUpdated', purchases[index]);
            return purchases[index];
        }
        return null;
    }

    deletePurchase(id) {
        const purchases = this.getPurchases();
        const index = purchases.findIndex(purchase => purchase.id === id);
        if (index !== -1) {
            const deleted = purchases.splice(index, 1)[0];
            localStorage.setItem(`${this.dbName}_purchases`, JSON.stringify(purchases));
            this.triggerEvent('purchaseDeleted', deleted);
            return deleted;
        }
        return null;
    }

    getPurchaseById(id) {
        return this.getPurchases().find(purchase => purchase.id === id);
    }

    // === وظائف المدفوعات والديون ===
    getPayments() {
        const payments = localStorage.getItem(`${this.dbName}_payments`);
        return payments ? JSON.parse(payments) : [];
    }

    addPayment(payment) {
        const payments = this.getPayments();
        payment.id = payment.id || this.generateId();
        payment.createdAt = payment.createdAt || new Date().toISOString();
        payments.push(payment);
        localStorage.setItem(`${this.dbName}_payments`, JSON.stringify(payments));
        this.triggerEvent('paymentAdded', payment);
        return payment;
    }

    getDebts() {
        const debts = localStorage.getItem(`${this.dbName}_debts`);
        return debts ? JSON.parse(debts) : [];
    }

    addDebt(debt) {
        const debts = this.getDebts();
        debt.id = debt.id || this.generateId();
        debt.createdAt = debt.createdAt || new Date().toISOString();
        debts.push(debt);
        localStorage.setItem(`${this.dbName}_debts`, JSON.stringify(debts));
        this.triggerEvent('debtAdded', debt);
        return debt;
    }

    // الحصول على العملاء المدينين
    getDebtorCustomers() {
        return this.getCustomers().filter(customer => customer.balance < 0);
    }

    // الحصول على إجمالي الديون
    getTotalDebts() {
        const customers = this.getCustomers();
        return customers.reduce((total, customer) => {
            return total + Math.abs(Math.min(0, customer.balance));
        }, 0);
    }

    // الحفظ التلقائي
    startAutoSave() {
        setInterval(() => {
            this.createBackup();
        }, 300000); // كل 5 دقائق
    }

    // إنشاء نسخة احتياطية
    createBackup() {
        const backup = {
            version: this.version,
            timestamp: new Date().toISOString(),
            data: {}
        };

        const tables = ['settings', 'categories', 'products', 'customers', 'suppliers', 'sales', 'sale_items', 'purchases', 'purchase_items', 'payments', 'debts'];
        
        tables.forEach(table => {
            const data = localStorage.getItem(`${this.dbName}_${table}`);
            if (data) {
                backup.data[table] = JSON.parse(data);
            }
        });

        localStorage.setItem(`${this.dbName}_backup`, JSON.stringify(backup));
        return backup;
    }

    // استعادة من النسخة الاحتياطية
    restoreBackup(backupData) {
        try {
            const backup = typeof backupData === 'string' ? JSON.parse(backupData) : backupData;
            
            Object.keys(backup.data).forEach(table => {
                localStorage.setItem(`${this.dbName}_${table}`, JSON.stringify(backup.data[table]));
            });

            this.triggerEvent('backupRestored', backup);
            return true;
        } catch (error) {
            console.error('خطأ في استعادة النسخة الاحتياطية:', error);
            return false;
        }
    }

    // تصدير البيانات
    exportData() {
        return this.createBackup();
    }

    // استيراد البيانات
    importData(data) {
        return this.restoreBackup(data);
    }

    // نظام الأحداث
    triggerEvent(eventName, data) {
        const event = new CustomEvent(`technoflash_${eventName}`, { detail: data });
        document.dispatchEvent(event);
    }

    // الاستماع للأحداث
    on(eventName, callback) {
        document.addEventListener(`technoflash_${eventName}`, callback);
    }

    // إزالة مستمع الحدث
    off(eventName, callback) {
        document.removeEventListener(`technoflash_${eventName}`, callback);
    }
}

// إنشاء مثيل قاعدة البيانات
const db = new TechnoFlashDB();
