<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تكنوفلاش</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: center;
            padding: 50px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار نظام تكنوفلاش</h1>
        <p>هذا اختبار لتشخيص المشاكل</p>
        
        <div id="status" class="status info">جاري التحقق من الملفات...</div>
        
        <div id="tests">
            <h3>نتائج الاختبار:</h3>
            <div id="testResults"></div>
        </div>
        
        <div>
            <button class="btn" onclick="runTests()">تشغيل الاختبارات</button>
            <button class="btn" onclick="testLogin()">اختبار تسجيل الدخول</button>
            <button class="btn" onclick="location.href='index.html'">العودة للنظام</button>
        </div>
    </div>

    <!-- تحميل ملفات JavaScript -->
    <script src="js/database.js"></script>
    <script src="js/app.js"></script>
    <script src="js/main.js"></script>
    <script src="js/dashboard.js"></script>

    <script>
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function addTestResult(test, result, details = '') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.innerHTML = `
                <p><strong>${test}:</strong> 
                <span style="color: ${result ? 'green' : 'red'}">${result ? 'نجح' : 'فشل'}</span>
                ${details ? `<br><small>${details}</small>` : ''}
                </p>
            `;
            results.appendChild(div);
        }

        function runTests() {
            document.getElementById('testResults').innerHTML = '';
            updateStatus('جاري تشغيل الاختبارات...', 'info');

            // اختبار تحميل قاعدة البيانات
            try {
                const dbExists = typeof db !== 'undefined';
                addTestResult('تحميل قاعدة البيانات', dbExists, dbExists ? 'تم تحميل db بنجاح' : 'متغير db غير موجود');
                
                if (dbExists) {
                    const settings = db.getSettings();
                    addTestResult('الحصول على الإعدادات', !!settings, settings ? 'تم الحصول على الإعدادات' : 'لا توجد إعدادات');
                }
            } catch (error) {
                addTestResult('تحميل قاعدة البيانات', false, 'خطأ: ' + error.message);
            }

            // اختبار تحميل التطبيق الرئيسي
            try {
                const appExists = typeof app !== 'undefined';
                addTestResult('تحميل التطبيق الرئيسي', appExists, appExists ? 'تم تحميل app بنجاح' : 'متغير app غير موجود');
            } catch (error) {
                addTestResult('تحميل التطبيق الرئيسي', false, 'خطأ: ' + error.message);
            }

            // اختبار تحميل لوحة المعلومات
            try {
                const dashboardExists = typeof loadDashboard !== 'undefined';
                addTestResult('تحميل لوحة المعلومات', dashboardExists, dashboardExists ? 'وظيفة loadDashboard موجودة' : 'وظيفة loadDashboard غير موجودة');
            } catch (error) {
                addTestResult('تحميل لوحة المعلومات', false, 'خطأ: ' + error.message);
            }

            // اختبار localStorage
            try {
                localStorage.setItem('test', 'test');
                const storageWorks = localStorage.getItem('test') === 'test';
                localStorage.removeItem('test');
                addTestResult('localStorage', storageWorks, storageWorks ? 'localStorage يعمل بشكل صحيح' : 'localStorage لا يعمل');
            } catch (error) {
                addTestResult('localStorage', false, 'خطأ: ' + error.message);
            }

            updateStatus('تم الانتهاء من الاختبارات', 'success');
        }

        function testLogin() {
            updateStatus('اختبار تسجيل الدخول...', 'info');
            
            try {
                if (typeof db === 'undefined') {
                    updateStatus('خطأ: قاعدة البيانات غير محملة', 'error');
                    return;
                }

                const settings = db.getSettings();
                if (!settings) {
                    updateStatus('خطأ: لا توجد إعدادات', 'error');
                    return;
                }

                const testPassword = '123';
                const isValid = db.verifyPassword(testPassword, settings.password);
                
                if (isValid) {
                    updateStatus('نجح اختبار تسجيل الدخول! كلمة المرور صحيحة', 'success');
                    
                    // محاولة تحميل لوحة المعلومات
                    if (typeof loadDashboard === 'function') {
                        updateStatus('تحميل لوحة المعلومات...', 'info');
                        setTimeout(() => {
                            try {
                                loadDashboard();
                                updateStatus('تم تحميل لوحة المعلومات بنجاح!', 'success');
                            } catch (error) {
                                updateStatus('خطأ في تحميل لوحة المعلومات: ' + error.message, 'error');
                            }
                        }, 500);
                    } else {
                        updateStatus('وظيفة loadDashboard غير موجودة', 'error');
                    }
                } else {
                    updateStatus('فشل اختبار تسجيل الدخول! كلمة المرور غير صحيحة', 'error');
                }
            } catch (error) {
                updateStatus('خطأ في اختبار تسجيل الدخول: ' + error.message, 'error');
            }
        }

        // تشغيل الاختبارات تلقائياً عند تحميل الصفحة
        window.onload = function() {
            setTimeout(runTests, 1000);
        };
    </script>
</body>
</html>
