<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل تكنوفلاش</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .fix-container {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 15px;
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            font-size: 16px;
        }
        .btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
        .info { background: rgba(33, 150, 243, 0.3); }
        .warning { background: rgba(255, 152, 0, 0.3); }
        .fix-section {
            background: rgba(255,255,255,0.05);
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            text-align: right;
        }
        h1, h2, h3 { text-align: center; }
        .code-block {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            text-align: left;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="fix-container">
        <h1>🔧 إصلاح مشاكل تكنوفلاش</h1>
        
        <div id="status" class="status info">جاري فحص النظام...</div>
        
        <div class="fix-section">
            <h3>🚀 إصلاحات سريعة</h3>
            <button class="btn" onclick="fixPassword()">🔑 إصلاح كلمة المرور</button>
            <button class="btn" onclick="clearData()">🗑️ مسح البيانات</button>
            <button class="btn" onclick="resetSystem()">🔄 إعادة تعيين النظام</button>
            <button class="btn" onclick="testSystem()">🧪 اختبار النظام</button>
        </div>

        <div class="fix-section">
            <h3>🔍 تشخيص المشاكل</h3>
            <div id="diagnostics"></div>
            <button class="btn" onclick="runDiagnostics()">تشغيل التشخيص</button>
        </div>

        <div class="fix-section">
            <h3>📋 حلول المشاكل الشائعة</h3>
            <div style="text-align: right;">
                <p><strong>❌ النظام لا يفتح:</strong></p>
                <ul>
                    <li>تأكد من تمكين JavaScript</li>
                    <li>امسح cache المتصفح</li>
                    <li>جرب متصفح آخر</li>
                </ul>
                
                <p><strong>❌ كلمة المرور لا تعمل:</strong></p>
                <ul>
                    <li>اضغط "إصلاح كلمة المرور" أعلاه</li>
                    <li>كلمة المرور الافتراضية: 123</li>
                </ul>
                
                <p><strong>❌ البيانات لا تظهر:</strong></p>
                <ul>
                    <li>تحقق من localStorage</li>
                    <li>أعد تحميل الصفحة</li>
                    <li>اضغط "إعادة تعيين النظام"</li>
                </ul>
            </div>
        </div>

        <div class="fix-section">
            <h3>🎯 روابط مفيدة</h3>
            <button class="btn" onclick="location.href='index.html'">🏠 العودة للنظام</button>
            <button class="btn" onclick="location.href='test.html'">🧪 صفحة الاختبار</button>
            <button class="btn" onclick="downloadBackup()">💾 تحميل نسخة احتياطية</button>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function fixPassword() {
            try {
                const defaultSettings = {
                    companyName: 'شركتي',
                    companyAddress: 'العنوان',
                    companyPhone: '0123456789',
                    companyEmail: '<EMAIL>',
                    taxRate: 15,
                    currency: 'ريال',
                    password: btoa('123' + 'technoflash_salt'),
                    theme: 'light',
                    language: 'ar'
                };
                localStorage.setItem('technoflash_pos_settings', JSON.stringify(defaultSettings));
                updateStatus('✅ تم إصلاح كلمة المرور! كلمة المرور الآن: 123', 'success');
            } catch (error) {
                updateStatus('❌ خطأ في إصلاح كلمة المرور: ' + error.message, 'error');
            }
        }

        function clearData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                try {
                    const keys = Object.keys(localStorage);
                    keys.forEach(key => {
                        if (key.includes('technoflash')) {
                            localStorage.removeItem(key);
                        }
                    });
                    updateStatus('✅ تم مسح جميع البيانات بنجاح', 'success');
                } catch (error) {
                    updateStatus('❌ خطأ في مسح البيانات: ' + error.message, 'error');
                }
            }
        }

        function resetSystem() {
            if (confirm('هل تريد إعادة تعيين النظام بالكامل؟')) {
                try {
                    localStorage.clear();
                    updateStatus('✅ تم إعادة تعيين النظام. أعد تحميل الصفحة', 'success');
                    setTimeout(() => location.reload(), 2000);
                } catch (error) {
                    updateStatus('❌ خطأ في إعادة التعيين: ' + error.message, 'error');
                }
            }
        }

        function testSystem() {
            location.href = 'test.html';
        }

        function runDiagnostics() {
            const diagnostics = document.getElementById('diagnostics');
            let html = '<div class="code-block">';
            
            // فحص localStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                html += '✅ localStorage يعمل<br>';
            } catch (e) {
                html += '❌ localStorage لا يعمل: ' + e.message + '<br>';
            }
            
            // فحص البيانات
            const settings = localStorage.getItem('technoflash_pos_settings');
            html += settings ? '✅ الإعدادات موجودة<br>' : '❌ الإعدادات مفقودة<br>';
            
            // فحص المتصفح
            html += '🌐 المتصفح: ' + navigator.userAgent.split(' ').pop() + '<br>';
            html += '📱 الجهاز: ' + (navigator.userAgentData?.mobile ? 'هاتف' : 'كمبيوتر') + '<br>';
            
            html += '</div>';
            diagnostics.innerHTML = html;
        }

        function downloadBackup() {
            try {
                const data = {};
                Object.keys(localStorage).forEach(key => {
                    if (key.includes('technoflash')) {
                        data[key] = localStorage.getItem(key);
                    }
                });
                
                const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'technoflash_backup_' + new Date().toISOString().split('T')[0] + '.json';
                a.click();
                URL.revokeObjectURL(url);
                
                updateStatus('✅ تم تحميل النسخة الاحتياطية', 'success');
            } catch (error) {
                updateStatus('❌ خطأ في تحميل النسخة الاحتياطية: ' + error.message, 'error');
            }
        }

        // تشغيل التشخيص تلقائياً
        window.onload = function() {
            setTimeout(runDiagnostics, 1000);
        };
    </script>
</body>
</html>
