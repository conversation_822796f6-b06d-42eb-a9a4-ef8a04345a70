{"name": "technoflash-pos", "version": "1.0.0", "description": "نظام إدارة نقاط البيع العربي - تكنوفلاش", "main": "index.html", "scripts": {"start": "electron .", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "serve": "python -m http.server 8000", "dev": "electron . --dev"}, "keywords": ["pos", "point-of-sale", "arabic", "retail", "inventory", "sales", "customers", "suppliers", "reports", "rtl", "تكنوفلاش", "نقاط البيع", "عربي"], "author": {"name": "تكنوفلاش", "email": "<EMAIL>", "url": "https://technoflash.com"}, "license": "MIT", "homepage": "https://github.com/technoflash/pos-system", "repository": {"type": "git", "url": "https://github.com/technoflash/pos-system.git"}, "bugs": {"url": "https://github.com/technoflash/pos-system/issues"}, "devDependencies": {"electron": "^latest", "electron-builder": "^latest"}, "build": {"appId": "com.technoflash.pos", "productName": "تكنوفلاش - نظام إدارة نقاط البيع", "directories": {"output": "dist"}, "files": ["index.html", "css/**/*", "js/**/*", "assets/**/*", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "engines": {"node": ">=14.0.0"}}