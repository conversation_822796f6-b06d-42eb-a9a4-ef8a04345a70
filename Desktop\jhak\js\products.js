// تكنوفلاش - إدارة المنتجات
class ProductsManager {
    constructor() {
        this.currentProduct = null;
        this.filteredProducts = [];
        this.currentFilter = 'all';
        this.currentSort = 'name';
        this.init();
    }

    init() {
        // إعداد مستمعي الأحداث
        this.setupEventListeners();
    }

    // تحميل صفحة المنتجات
    load() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getHTML();

        // تحميل البيانات
        this.loadData();

        // إعداد الأحداث
        this.setupEvents();
    }

    // الحصول على HTML صفحة المنتجات
    getHTML() {
        return `
            <div class="products-page fade-in">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="page-title">
                        <h1><i class="fas fa-box"></i> إدارة المنتجات</h1>
                        <p>إضافة وتعديل وإدارة المنتجات والفئات</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="productsManager.showAddProductModal()">
                            <i class="fas fa-plus"></i>
                            إضافة منتج
                        </button>
                        <button class="btn btn-secondary" onclick="productsManager.showCategoriesModal()">
                            <i class="fas fa-tags"></i>
                            إدارة الفئات
                        </button>
                        <button class="btn btn-success" onclick="productsManager.exportProducts()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                </div>

                <!-- أدوات البحث والفلترة -->
                <div class="products-toolbar">
                    <div class="search-section">
                        <div class="search-box">
                            <input type="text" id="productSearch" placeholder="البحث في المنتجات..."
                                   onkeyup="productsManager.searchProducts(this.value)">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>

                    <div class="filter-section">
                        <select id="categoryFilter" onchange="productsManager.filterByCategory(this.value)">
                            <option value="all">جميع الفئات</option>
                        </select>

                        <select id="stockFilter" onchange="productsManager.filterByStock(this.value)">
                            <option value="all">جميع المنتجات</option>
                            <option value="in_stock">متوفر</option>
                            <option value="low_stock">مخزون منخفض</option>
                            <option value="out_of_stock">نفد المخزون</option>
                        </select>

                        <select id="sortBy" onchange="productsManager.sortProducts(this.value)">
                            <option value="name">ترتيب بالاسم</option>
                            <option value="price">ترتيب بالسعر</option>
                            <option value="quantity">ترتيب بالكمية</option>
                            <option value="created">ترتيب بالتاريخ</option>
                        </select>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="products-stats">
                    <div class="stat-item">
                        <div class="stat-icon products">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalProductsCount">٠</h3>
                            <p>إجمالي المنتجات</p>
                        </div>
                    </div>

                    <div class="stat-item">
                        <div class="stat-icon categories">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalCategoriesCount">٠</h3>
                            <p>عدد الفئات</p>
                        </div>
                    </div>

                    <div class="stat-item">
                        <div class="stat-icon value">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalInventoryValue">٠</h3>
                            <p>قيمة المخزون</p>
                        </div>
                    </div>

                    <div class="stat-item">
                        <div class="stat-icon warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="lowStockCount">٠</h3>
                            <p>مخزون منخفض</p>
                        </div>
                    </div>
                </div>

                <!-- جدول المنتجات -->
                <div class="products-table-container">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">قائمة المنتجات</h3>
                            <div class="table-actions">
                                <button class="btn btn-sm btn-secondary" onclick="productsManager.refreshProducts()">
                                    <i class="fas fa-refresh"></i>
                                    تحديث
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="productsTableContainer">
                                <!-- سيتم تحميل الجدول هنا -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نافذة إضافة/تعديل منتج -->
            <div id="productModal" class="modal hidden">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3 id="productModalTitle">إضافة منتج جديد</h3>
                        <button class="modal-close" onclick="productsManager.hideProductModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <form id="productForm" class="modal-body validate-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="productName">اسم المنتج *</label>
                                <input type="text" id="productName" name="name"
                                       class="validate-field" data-rules="required|min:2"
                                       placeholder="أدخل اسم المنتج">
                            </div>
                            <div class="form-group">
                                <label for="productCategory">الفئة *</label>
                                <select id="productCategory" name="categoryId"
                                        class="validate-field" data-rules="required">
                                    <option value="">اختر الفئة</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="productPrice">السعر *</label>
                                <input type="text" id="productPrice" name="price"
                                       class="validate-field" data-rules="required|number|positive"
                                       placeholder="٠.٠٠">
                            </div>
                            <div class="form-group">
                                <label for="productCost">سعر التكلفة</label>
                                <input type="text" id="productCost" name="cost"
                                       class="validate-field" data-rules="number|positive"
                                       placeholder="٠.٠٠">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="productQuantity">الكمية *</label>
                                <input type="text" id="productQuantity" name="quantity"
                                       class="validate-field" data-rules="required|number|positive"
                                       placeholder="٠">
                            </div>
                            <div class="form-group">
                                <label for="productMinStock">الحد الأدنى للمخزون</label>
                                <input type="text" id="productMinStock" name="minStock"
                                       class="validate-field" data-rules="number|positive"
                                       placeholder="٠">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="productBarcode">الباركود</label>
                                <input type="text" id="productBarcode" name="barcode"
                                       placeholder="أدخل الباركود أو اتركه فارغاً للتوليد التلقائي">
                            </div>
                            <div class="form-group">
                                <label for="productUnit">الوحدة</label>
                                <select id="productUnit" name="unit">
                                    <option value="قطعة">قطعة</option>
                                    <option value="كيلو">كيلو</option>
                                    <option value="لتر">لتر</option>
                                    <option value="متر">متر</option>
                                    <option value="علبة">علبة</option>
                                    <option value="كرتون">كرتون</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="productDescription">الوصف</label>
                            <textarea id="productDescription" name="description"
                                      placeholder="وصف المنتج (اختياري)"></textarea>
                        </div>

                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="productActive" name="active" checked>
                                منتج نشط
                            </label>
                        </div>
                    </form>
                    <div class="modal-footer">
                        <button type="submit" form="productForm" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ
                        </button>
                        <button class="btn btn-secondary" onclick="productsManager.hideProductModal()">
                            إلغاء
                        </button>
                    </div>
                </div>
            </div>

            <!-- نافذة إدارة الفئات -->
            <div id="categoriesModal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>إدارة الفئات</h3>
                        <button class="modal-close" onclick="productsManager.hideCategoriesModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <!-- نموذج إضافة فئة -->
                        <form id="categoryForm" class="category-form validate-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <input type="text" id="categoryName" name="name"
                                           class="validate-field" data-rules="required|min:2"
                                           placeholder="اسم الفئة الجديدة">
                                </div>
                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-plus"></i>
                                        إضافة
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- قائمة الفئات -->
                        <div id="categoriesList" class="categories-list">
                            <!-- سيتم تحميل الفئات هنا -->
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // الاستماع لأحداث قاعدة البيانات
        db.on('productAdded', () => this.refreshProducts());
        db.on('productUpdated', () => this.refreshProducts());
        db.on('productDeleted', () => this.refreshProducts());
        db.on('categoryAdded', () => this.loadCategories());
        db.on('categoryUpdated', () => this.loadCategories());
        db.on('categoryDeleted', () => this.loadCategories());
    }

    // تحميل البيانات
    loadData() {
        this.loadProducts();
        this.loadCategories();
        this.loadStats();
    }

    // إعداد الأحداث
    setupEvents() {
        // نموذج المنتج
        const productForm = document.getElementById('productForm');
        if (productForm) {
            productForm.addEventListener('submit', (e) => this.handleProductSubmit(e));
        }

        // نموذج الفئة
        const categoryForm = document.getElementById('categoryForm');
        if (categoryForm) {
            categoryForm.addEventListener('submit', (e) => this.handleCategorySubmit(e));
        }
    }

    // تحميل المنتجات
    loadProducts() {
        const products = db.getProducts();
        this.filteredProducts = [...products];
        this.displayProducts();
    }

    // عرض المنتجات
    displayProducts() {
        const container = document.getElementById('productsTableContainer');

        if (this.filteredProducts.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-box-open"></i>
                    <h3>لا توجد منتجات</h3>
                    <p>ابدأ بإضافة منتج جديد</p>
                    <button class="btn btn-primary" onclick="productsManager.showAddProductModal()">
                        <i class="fas fa-plus"></i>
                        إضافة منتج
                    </button>
                </div>
            `;
            return;
        }

        const columns = [
            { key: 'name', title: 'اسم المنتج' },
            {
                key: 'categoryId',
                title: 'الفئة',
                format: (value) => {
                    const category = db.getCategoryById(value);
                    return category ? category.name : 'غير محدد';
                }
            },
            {
                key: 'price',
                title: 'السعر',
                format: (value) => app.formatCurrency(value)
            },
            {
                key: 'quantity',
                title: 'الكمية',
                format: (value, row) => {
                    const quantity = db.toArabicNumbers(value);
                    const minStock = row.minStock || 10;
                    const className = value <= minStock ? 'low-stock' : '';
                    return `<span class="${className}">${quantity} ${row.unit || 'قطعة'}</span>`;
                }
            },
            {
                key: 'active',
                title: 'الحالة',
                format: (value) => value ?
                    '<span class="status active">نشط</span>' :
                    '<span class="status inactive">غير نشط</span>'
            },
            {
                key: 'actions',
                title: 'الإجراءات',
                format: (value, row) => `
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="productsManager.editProduct('${row.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="productsManager.deleteProduct('${row.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="productsManager.viewProduct('${row.id}')" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                `
            }
        ];

        appCore.createSortableTable(this.filteredProducts, columns, 'productsTableContainer');
    }

    // تحميل الفئات
    loadCategories() {
        const categories = db.getCategories();

        // تحديث قائمة الفئات في الفلتر
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.innerHTML = '<option value="all">جميع الفئات</option>';
            categories.forEach(category => {
                categoryFilter.innerHTML += `<option value="${category.id}">${category.name}</option>`;
            });
        }

        // تحديث قائمة الفئات في نموذج المنتج
        const productCategory = document.getElementById('productCategory');
        if (productCategory) {
            productCategory.innerHTML = '<option value="">اختر الفئة</option>';
            categories.forEach(category => {
                productCategory.innerHTML += `<option value="${category.id}">${category.name}</option>`;
            });
        }

        // عرض الفئات في نافذة إدارة الفئات
        this.displayCategories(categories);
    }

    // عرض الفئات
    displayCategories(categories) {
        const container = document.getElementById('categoriesList');
        if (!container) return;

        if (categories.length === 0) {
            container.innerHTML = '<div class="no-categories">لا توجد فئات</div>';
            return;
        }

        let html = '';
        categories.forEach(category => {
            const productsCount = db.getProducts().filter(p => p.categoryId === category.id).length;
            html += `
                <div class="category-item">
                    <div class="category-info">
                        <h4>${category.name}</h4>
                        <small>${db.toArabicNumbers(productsCount)} منتج</small>
                    </div>
                    <div class="category-actions">
                        <button class="btn btn-sm btn-primary" onclick="productsManager.editCategory('${category.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="productsManager.deleteCategory('${category.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    // تحميل الإحصائيات
    loadStats() {
        const products = db.getProducts();
        const categories = db.getCategories();

        // إجمالي المنتجات
        document.getElementById('totalProductsCount').textContent = db.toArabicNumbers(products.length);

        // عدد الفئات
        document.getElementById('totalCategoriesCount').textContent = db.toArabicNumbers(categories.length);

        // قيمة المخزون
        const inventoryValue = products.reduce((sum, product) => sum + (product.price * product.quantity), 0);
        document.getElementById('totalInventoryValue').textContent = app.formatCurrency(inventoryValue);

        // المخزون المنخفض
        const lowStockProducts = db.getLowStockProducts();
        document.getElementById('lowStockCount').textContent = db.toArabicNumbers(lowStockProducts.length);
    }

    // البحث في المنتجات
    searchProducts(query) {
        if (!query.trim()) {
            this.filteredProducts = db.getProducts();
        } else {
            this.filteredProducts = db.searchProducts(query);
        }
        this.displayProducts();
    }

    // فلترة حسب الفئة
    filterByCategory(categoryId) {
        const products = db.getProducts();
        if (categoryId === 'all') {
            this.filteredProducts = products;
        } else {
            this.filteredProducts = products.filter(product => product.categoryId === categoryId);
        }
        this.displayProducts();
    }

    // فلترة حسب المخزون
    filterByStock(stockType) {
        const products = db.getProducts();
        switch (stockType) {
            case 'in_stock':
                this.filteredProducts = products.filter(product => product.quantity > (product.minStock || 10));
                break;
            case 'low_stock':
                this.filteredProducts = products.filter(product =>
                    product.quantity <= (product.minStock || 10) && product.quantity > 0);
                break;
            case 'out_of_stock':
                this.filteredProducts = products.filter(product => product.quantity === 0);
                break;
            default:
                this.filteredProducts = products;
        }
        this.displayProducts();
    }

    // ترتيب المنتجات
    sortProducts(sortBy) {
        this.filteredProducts.sort((a, b) => {
            switch (sortBy) {
                case 'name':
                    return a.name.localeCompare(b.name, 'ar');
                case 'price':
                    return a.price - b.price;
                case 'quantity':
                    return b.quantity - a.quantity;
                case 'created':
                    return new Date(b.createdAt) - new Date(a.createdAt);
                default:
                    return 0;
            }
        });
        this.displayProducts();
    }

    // تحديث المنتجات
    refreshProducts() {
        this.loadData();
        app.showAlert('تم تحديث قائمة المنتجات', 'success');
    }

    // عرض نافذة إضافة منتج
    showAddProductModal() {
        this.currentProduct = null;
        document.getElementById('productModalTitle').textContent = 'إضافة منتج جديد';
        this.resetProductForm();
        document.getElementById('productModal').classList.remove('hidden');
        document.getElementById('productName').focus();
    }

    // عرض نافذة تعديل منتج
    editProduct(productId) {
        const product = db.getProductById(productId);
        if (!product) {
            app.showAlert('المنتج غير موجود', 'error');
            return;
        }

        this.currentProduct = product;
        document.getElementById('productModalTitle').textContent = 'تعديل المنتج';
        this.fillProductForm(product);
        document.getElementById('productModal').classList.remove('hidden');
        document.getElementById('productName').focus();
    }

    // عرض تفاصيل المنتج
    viewProduct(productId) {
        const product = db.getProductById(productId);
        if (!product) {
            app.showAlert('المنتج غير موجود', 'error');
            return;
        }

        const category = db.getCategoryById(product.categoryId);
        const categoryName = category ? category.name : 'غير محدد';

        const content = `
            <div class="product-details">
                <h2>${product.name}</h2>
                <div class="product-info">
                    <div class="info-row">
                        <strong>الفئة:</strong> ${categoryName}
                    </div>
                    <div class="info-row">
                        <strong>السعر:</strong> ${app.formatCurrency(product.price)}
                    </div>
                    <div class="info-row">
                        <strong>سعر التكلفة:</strong> ${app.formatCurrency(product.cost || 0)}
                    </div>
                    <div class="info-row">
                        <strong>الكمية:</strong> ${db.toArabicNumbers(product.quantity)} ${product.unit || 'قطعة'}
                    </div>
                    <div class="info-row">
                        <strong>الحد الأدنى:</strong> ${db.toArabicNumbers(product.minStock || 0)}
                    </div>
                    <div class="info-row">
                        <strong>الباركود:</strong> ${product.barcode || 'غير محدد'}
                    </div>
                    <div class="info-row">
                        <strong>الحالة:</strong> ${product.active ? 'نشط' : 'غير نشط'}
                    </div>
                    ${product.description ? `<div class="info-row"><strong>الوصف:</strong> ${product.description}</div>` : ''}
                    <div class="info-row">
                        <strong>تاريخ الإضافة:</strong> ${app.formatDate(product.createdAt)}
                    </div>
                </div>
            </div>
        `;

        app.printContent(content);
    }

    // حذف منتج
    deleteProduct(productId) {
        const product = db.getProductById(productId);
        if (!product) {
            app.showAlert('المنتج غير موجود', 'error');
            return;
        }

        app.showConfirm(
            'حذف المنتج',
            `هل أنت متأكد من حذف المنتج "${product.name}"؟`,
            () => {
                if (db.deleteProduct(productId)) {
                    app.showAlert('تم حذف المنتج بنجاح', 'success');
                } else {
                    app.showAlert('حدث خطأ أثناء حذف المنتج', 'error');
                }
            }
        );
    }

    // إخفاء نافذة المنتج
    hideProductModal() {
        document.getElementById('productModal').classList.add('hidden');
        this.resetProductForm();
        this.currentProduct = null;
    }

    // إعادة تعيين نموذج المنتج
    resetProductForm() {
        document.getElementById('productForm').reset();
        document.getElementById('productActive').checked = true;

        // إزالة رسائل الخطأ
        document.querySelectorAll('.field-error').forEach(error => error.remove());
        document.querySelectorAll('.error').forEach(field => field.classList.remove('error'));
    }

    // ملء نموذج المنتج
    fillProductForm(product) {
        document.getElementById('productName').value = product.name;
        document.getElementById('productCategory').value = product.categoryId || '';
        document.getElementById('productPrice').value = product.price;
        document.getElementById('productCost').value = product.cost || '';
        document.getElementById('productQuantity').value = product.quantity;
        document.getElementById('productMinStock').value = product.minStock || '';
        document.getElementById('productBarcode').value = product.barcode || '';
        document.getElementById('productUnit').value = product.unit || 'قطعة';
        document.getElementById('productDescription').value = product.description || '';
        document.getElementById('productActive').checked = product.active !== false;
    }

    // معالجة إرسال نموذج المنتج
    handleProductSubmit(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const productData = {
            name: app.sanitizeText(formData.get('name')),
            categoryId: formData.get('categoryId'),
            price: app.parseNumber(formData.get('price')),
            cost: app.parseNumber(formData.get('cost')) || 0,
            quantity: app.parseNumber(formData.get('quantity')),
            minStock: app.parseNumber(formData.get('minStock')) || 10,
            barcode: formData.get('barcode') || this.generateBarcode(),
            unit: formData.get('unit') || 'قطعة',
            description: app.sanitizeText(formData.get('description')),
            active: formData.get('active') === 'on'
        };

        if (this.currentProduct) {
            // تحديث منتج موجود
            if (db.updateProduct(this.currentProduct.id, productData)) {
                app.showAlert('تم تحديث المنتج بنجاح', 'success');
                this.hideProductModal();
            } else {
                app.showAlert('حدث خطأ أثناء تحديث المنتج', 'error');
            }
        } else {
            // إضافة منتج جديد
            if (db.addProduct(productData)) {
                app.showAlert('تم إضافة المنتج بنجاح', 'success');
                this.hideProductModal();
            } else {
                app.showAlert('حدث خطأ أثناء إضافة المنتج', 'error');
            }
        }
    }

    // توليد باركود
    generateBarcode() {
        return Date.now().toString() + Math.random().toString(36).substr(2, 5);
    }

    // عرض نافذة إدارة الفئات
    showCategoriesModal() {
        this.loadCategories();
        document.getElementById('categoriesModal').classList.remove('hidden');
        document.getElementById('categoryName').focus();
    }

    // إخفاء نافذة إدارة الفئات
    hideCategoriesModal() {
        document.getElementById('categoriesModal').classList.add('hidden');
        document.getElementById('categoryForm').reset();
    }

    // معالجة إرسال نموذج الفئة
    handleCategorySubmit(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const categoryData = {
            name: app.sanitizeText(formData.get('name')),
            description: ''
        };

        if (db.addCategory(categoryData)) {
            app.showAlert('تم إضافة الفئة بنجاح', 'success');
            document.getElementById('categoryForm').reset();
            document.getElementById('categoryName').focus();
        } else {
            app.showAlert('حدث خطأ أثناء إضافة الفئة', 'error');
        }
    }

    // تعديل فئة
    editCategory(categoryId) {
        const category = db.getCategoryById(categoryId);
        if (!category) {
            app.showAlert('الفئة غير موجودة', 'error');
            return;
        }

        const newName = prompt('اسم الفئة الجديد:', category.name);
        if (newName && newName.trim() !== category.name) {
            if (db.updateCategory(categoryId, { name: app.sanitizeText(newName) })) {
                app.showAlert('تم تحديث الفئة بنجاح', 'success');
            } else {
                app.showAlert('حدث خطأ أثناء تحديث الفئة', 'error');
            }
        }
    }

    // حذف فئة
    deleteCategory(categoryId) {
        const category = db.getCategoryById(categoryId);
        if (!category) {
            app.showAlert('الفئة غير موجودة', 'error');
            return;
        }

        // التحقق من وجود منتجات في هذه الفئة
        const productsInCategory = db.getProducts().filter(p => p.categoryId === categoryId);
        if (productsInCategory.length > 0) {
            app.showAlert(`لا يمكن حذف الفئة لأنها تحتوي على ${db.toArabicNumbers(productsInCategory.length)} منتج`, 'warning');
            return;
        }

        app.showConfirm(
            'حذف الفئة',
            `هل أنت متأكد من حذف الفئة "${category.name}"؟`,
            () => {
                if (db.deleteCategory(categoryId)) {
                    app.showAlert('تم حذف الفئة بنجاح', 'success');
                } else {
                    app.showAlert('حدث خطأ أثناء حذف الفئة', 'error');
                }
            }
        );
    }

    // تصدير المنتجات
    exportProducts() {
        const products = db.getProducts();
        const categories = db.getCategories();

        const exportData = products.map(product => {
            const category = categories.find(c => c.id === product.categoryId);
            return {
                'اسم المنتج': product.name,
                'الفئة': category ? category.name : 'غير محدد',
                'السعر': product.price,
                'سعر التكلفة': product.cost || 0,
                'الكمية': product.quantity,
                'الوحدة': product.unit || 'قطعة',
                'الحد الأدنى': product.minStock || 0,
                'الباركود': product.barcode || '',
                'الحالة': product.active ? 'نشط' : 'غير نشط',
                'الوصف': product.description || '',
                'تاريخ الإضافة': app.formatDate(product.createdAt)
            };
        });

        const columns = [
            { key: 'اسم المنتج', title: 'اسم المنتج' },
            { key: 'الفئة', title: 'الفئة' },
            { key: 'السعر', title: 'السعر' },
            { key: 'سعر التكلفة', title: 'سعر التكلفة' },
            { key: 'الكمية', title: 'الكمية' },
            { key: 'الوحدة', title: 'الوحدة' },
            { key: 'الحد الأدنى', title: 'الحد الأدنى' },
            { key: 'الباركود', title: 'الباركود' },
            { key: 'الحالة', title: 'الحالة' },
            { key: 'الوصف', title: 'الوصف' },
            { key: 'تاريخ الإضافة', title: 'تاريخ الإضافة' }
        ];

        const filename = `المنتجات_${new Date().toISOString().split('T')[0]}.csv`;
        appCore.exportToCSV(exportData, filename, columns);

        app.showAlert('تم تصدير المنتجات بنجاح', 'success');
    }
}

// إنشاء مثيل إدارة المنتجات
const productsManager = new ProductsManager();

// وظيفة تحميل صفحة المنتجات
function loadProducts() {
    if (typeof app !== 'undefined') {
        app.currentPage = 'products';
        app.updateActiveNavItem('products');
    }
    productsManager.load();
}