// تكنوفلاش - وظائف التطبيق الأساسية
class AppCore {
    constructor() {
        this.currentView = null;
        this.viewHistory = [];
        this.searchCache = new Map();
        this.init();
    }

    init() {
        this.setupGlobalEventListeners();
        this.setupSearchFunctionality();
        this.setupFormValidation();
    }

    // إعداد مستمعي الأحداث العامة
    setupGlobalEventListeners() {
        // البحث العام
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                this.showGlobalSearch();
            }
        });

        // حفظ البيانات عند إغلاق النافذة
        window.addEventListener('beforeunload', () => {
            this.saveCurrentState();
        });

        // تحديث الوقت كل دقيقة
        setInterval(() => {
            this.updateTimeDisplays();
        }, 60000);
    }

    // إعداد وظائف البحث
    setupSearchFunctionality() {
        // إنشاء مربع البحث العام
        this.createGlobalSearchBox();
    }

    // إنشاء مربع البحث العام
    createGlobalSearchBox() {
        const searchBox = document.createElement('div');
        searchBox.id = 'globalSearchBox';
        searchBox.className = 'global-search-box hidden';
        searchBox.innerHTML = `
            <div class="search-container">
                <div class="search-header">
                    <h3>البحث العام</h3>
                    <button onclick="this.hideGlobalSearch()" class="search-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="search-input-container">
                    <input type="text" id="globalSearchInput" placeholder="ابحث في المنتجات، العملاء، الفواتير...">
                    <i class="fas fa-search"></i>
                </div>
                <div id="searchResults" class="search-results"></div>
            </div>
        `;
        document.body.appendChild(searchBox);

        // إعداد البحث التلقائي
        const searchInput = document.getElementById('globalSearchInput');
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.performGlobalSearch(e.target.value);
            }, 300);
        });
    }

    // عرض البحث العام
    showGlobalSearch() {
        const searchBox = document.getElementById('globalSearchBox');
        searchBox.classList.remove('hidden');
        document.getElementById('globalSearchInput').focus();
    }

    // إخفاء البحث العام
    hideGlobalSearch() {
        const searchBox = document.getElementById('globalSearchBox');
        searchBox.classList.add('hidden');
        document.getElementById('globalSearchInput').value = '';
        document.getElementById('searchResults').innerHTML = '';
    }

    // تنفيذ البحث العام
    performGlobalSearch(query) {
        if (!query || query.length < 2) {
            document.getElementById('searchResults').innerHTML = '';
            return;
        }

        // التحقق من الكاش
        if (this.searchCache.has(query)) {
            this.displaySearchResults(this.searchCache.get(query));
            return;
        }

        const results = {
            products: [],
            customers: [],
            suppliers: [],
            sales: []
        };

        // البحث في المنتجات
        const products = db.searchProducts(query);
        results.products = products.slice(0, 5);

        // البحث في العملاء
        const customers = db.getCustomers().filter(customer => 
            customer.name.toLowerCase().includes(query.toLowerCase()) ||
            customer.phone.includes(query) ||
            customer.email.toLowerCase().includes(query.toLowerCase())
        );
        results.customers = customers.slice(0, 5);

        // البحث في الموردين
        const suppliers = db.getSuppliers().filter(supplier => 
            supplier.name.toLowerCase().includes(query.toLowerCase()) ||
            supplier.phone.includes(query) ||
            supplier.email.toLowerCase().includes(query.toLowerCase())
        );
        results.suppliers = suppliers.slice(0, 5);

        // حفظ في الكاش
        this.searchCache.set(query, results);

        this.displaySearchResults(results);
    }

    // عرض نتائج البحث
    displaySearchResults(results) {
        const resultsContainer = document.getElementById('searchResults');
        let html = '';

        if (results.products.length > 0) {
            html += '<div class="search-section"><h4>المنتجات</h4>';
            results.products.forEach(product => {
                html += `
                    <div class="search-result-item" onclick="this.viewProduct('${product.id}')">
                        <i class="fas fa-box"></i>
                        <div>
                            <strong>${product.name}</strong>
                            <small>${app.formatCurrency(product.price)} - الكمية: ${db.toArabicNumbers(product.quantity)}</small>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
        }

        if (results.customers.length > 0) {
            html += '<div class="search-section"><h4>العملاء</h4>';
            results.customers.forEach(customer => {
                html += `
                    <div class="search-result-item" onclick="this.viewCustomer('${customer.id}')">
                        <i class="fas fa-user"></i>
                        <div>
                            <strong>${customer.name}</strong>
                            <small>${customer.phone} - الرصيد: ${app.formatCurrency(customer.balance)}</small>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
        }

        if (results.suppliers.length > 0) {
            html += '<div class="search-section"><h4>الموردين</h4>';
            results.suppliers.forEach(supplier => {
                html += `
                    <div class="search-result-item" onclick="this.viewSupplier('${supplier.id}')">
                        <i class="fas fa-truck"></i>
                        <div>
                            <strong>${supplier.name}</strong>
                            <small>${supplier.phone}</small>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
        }

        if (html === '') {
            html = '<div class="no-results">لا توجد نتائج للبحث</div>';
        }

        resultsContainer.innerHTML = html;
    }

    // إعداد التحقق من صحة النماذج
    setupFormValidation() {
        document.addEventListener('submit', (e) => {
            if (e.target && e.target.classList && e.target.classList.contains('validate-form')) {
                if (!this.validateForm(e.target)) {
                    e.preventDefault();
                }
            }
        });

        // التحقق الفوري من الحقول
        document.addEventListener('blur', (e) => {
            if (e.target && e.target.classList && e.target.classList.contains('validate-field')) {
                this.validateField(e.target);
            }
        }, true);
    }

    // التحقق من صحة النموذج
    validateForm(form) {
        let isValid = true;
        const fields = form.querySelectorAll('.validate-field');
        
        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }

    // التحقق من صحة الحقل
    validateField(field) {
        const value = field.value.trim();
        const rules = field.dataset.rules ? field.dataset.rules.split('|') : [];
        let isValid = true;
        let errorMessage = '';

        // إزالة رسائل الخطأ السابقة
        this.clearFieldError(field);

        // التحقق من القواعد
        for (const rule of rules) {
            const [ruleName, ruleValue] = rule.split(':');
            
            switch (ruleName) {
                case 'required':
                    if (!value) {
                        errorMessage = 'هذا الحقل مطلوب';
                        isValid = false;
                    }
                    break;
                    
                case 'email':
                    if (value && !app.isValidEmail(value)) {
                        errorMessage = 'البريد الإلكتروني غير صحيح';
                        isValid = false;
                    }
                    break;
                    
                case 'phone':
                    if (value && !app.isValidPhone(value)) {
                        errorMessage = 'رقم الهاتف غير صحيح';
                        isValid = false;
                    }
                    break;
                    
                case 'min':
                    if (value && value.length < parseInt(ruleValue)) {
                        errorMessage = `يجب أن يكون ${db.toArabicNumbers(ruleValue)} أحرف على الأقل`;
                        isValid = false;
                    }
                    break;
                    
                case 'max':
                    if (value && value.length > parseInt(ruleValue)) {
                        errorMessage = `يجب أن يكون ${db.toArabicNumbers(ruleValue)} أحرف كحد أقصى`;
                        isValid = false;
                    }
                    break;
                    
                case 'number':
                    if (value && isNaN(app.parseNumber(value))) {
                        errorMessage = 'يجب أن يكون رقماً صحيحاً';
                        isValid = false;
                    }
                    break;
                    
                case 'positive':
                    if (value && app.parseNumber(value) <= 0) {
                        errorMessage = 'يجب أن يكون رقماً موجباً';
                        isValid = false;
                    }
                    break;
            }
            
            if (!isValid) break;
        }

        // عرض رسالة الخطأ
        if (!isValid) {
            this.showFieldError(field, errorMessage);
        }

        return isValid;
    }

    // عرض خطأ الحقل
    showFieldError(field, message) {
        field.classList.add('error');
        
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.textContent = message;
        
        field.parentNode.appendChild(errorElement);
    }

    // إزالة خطأ الحقل
    clearFieldError(field) {
        field.classList.remove('error');
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
    }

    // حفظ الحالة الحالية
    saveCurrentState() {
        const state = {
            currentView: this.currentView,
            timestamp: new Date().toISOString()
        };
        localStorage.setItem('technoflash_app_state', JSON.stringify(state));
    }

    // استعادة الحالة المحفوظة
    restoreState() {
        const savedState = localStorage.getItem('technoflash_app_state');
        if (savedState) {
            try {
                const state = JSON.parse(savedState);
                this.currentView = state.currentView;
            } catch (error) {
                console.error('خطأ في استعادة الحالة:', error);
            }
        }
    }

    // تحديث عروض الوقت
    updateTimeDisplays() {
        const timeElements = document.querySelectorAll('.current-time');
        const now = new Date();
        const timeString = app.formatTime(now);
        
        timeElements.forEach(element => {
            element.textContent = timeString;
        });
    }

    // إنشاء جدول قابل للفرز
    createSortableTable(data, columns, containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        let html = `
            <div class="table-container">
                <table class="table sortable-table">
                    <thead>
                        <tr>
        `;

        columns.forEach(column => {
            html += `
                <th data-sort="${column.key}" class="sortable">
                    ${column.title}
                    <i class="fas fa-sort sort-icon"></i>
                </th>
            `;
        });

        html += `
                        </tr>
                    </thead>
                    <tbody id="${containerId}_tbody">
        `;

        data.forEach(row => {
            html += '<tr>';
            columns.forEach(column => {
                let value = row[column.key];
                if (column.format) {
                    value = column.format(value, row);
                }
                html += `<td>${value}</td>`;
            });
            html += '</tr>';
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = html;

        // إعداد الفرز
        this.setupTableSorting(containerId, data, columns);
    }

    // إعداد فرز الجدول
    setupTableSorting(containerId, data, columns) {
        const headers = document.querySelectorAll(`#${containerId} .sortable`);
        let sortDirection = 'asc';
        let sortColumn = null;

        headers.forEach(header => {
            header.addEventListener('click', () => {
                const column = header.dataset.sort;
                
                // تحديث اتجاه الفرز
                if (sortColumn === column) {
                    sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
                } else {
                    sortDirection = 'asc';
                    sortColumn = column;
                }

                // إزالة الفئات السابقة
                headers.forEach(h => {
                    h.classList.remove('sort-asc', 'sort-desc');
                });

                // إضافة الفئة الجديدة
                header.classList.add(`sort-${sortDirection}`);

                // فرز البيانات
                const sortedData = [...data].sort((a, b) => {
                    let aVal = a[column];
                    let bVal = b[column];

                    // التعامل مع الأرقام
                    if (typeof aVal === 'string' && !isNaN(app.parseNumber(aVal))) {
                        aVal = app.parseNumber(aVal);
                        bVal = app.parseNumber(bVal);
                    }

                    if (sortDirection === 'asc') {
                        return aVal > bVal ? 1 : -1;
                    } else {
                        return aVal < bVal ? 1 : -1;
                    }
                });

                // تحديث الجدول
                this.updateTableBody(`${containerId}_tbody`, sortedData, columns);
            });
        });
    }

    // تحديث محتوى الجدول
    updateTableBody(tbodyId, data, columns) {
        const tbody = document.getElementById(tbodyId);
        let html = '';

        data.forEach(row => {
            html += '<tr>';
            columns.forEach(column => {
                let value = row[column.key];
                if (column.format) {
                    value = column.format(value, row);
                }
                html += `<td>${value}</td>`;
            });
            html += '</tr>';
        });

        tbody.innerHTML = html;
    }

    // إنشاء مخطط بسيط
    createSimpleChart(data, containerId, type = 'bar') {
        const container = document.getElementById(containerId);
        if (!container) return;

        // مخطط بسيط باستخدام CSS
        let html = '<div class="simple-chart">';
        
        if (type === 'bar') {
            const maxValue = Math.max(...data.map(item => item.value));
            
            data.forEach(item => {
                const percentage = (item.value / maxValue) * 100;
                html += `
                    <div class="chart-bar">
                        <div class="bar-label">${item.label}</div>
                        <div class="bar-container">
                            <div class="bar-fill" style="width: ${percentage}%"></div>
                            <div class="bar-value">${app.formatNumber(item.value)}</div>
                        </div>
                    </div>
                `;
            });
        }

        html += '</div>';
        container.innerHTML = html;
    }

    // تصدير البيانات إلى CSV
    exportToCSV(data, filename, columns) {
        let csv = '';
        
        // إضافة العناوين
        csv += columns.map(col => col.title).join(',') + '\n';
        
        // إضافة البيانات
        data.forEach(row => {
            const values = columns.map(col => {
                let value = row[col.key];
                if (col.format && typeof col.format === 'function') {
                    value = col.format(value, row);
                }
                // تنظيف القيمة من الفواصل والعلامات
                return `"${String(value).replace(/"/g, '""')}"`;
            });
            csv += values.join(',') + '\n';
        });

        // تحميل الملف
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
    }

    // طباعة الجدول
    printTable(tableId, title) {
        const table = document.getElementById(tableId);
        if (!table) return;

        const printContent = `
            <div class="print-header">
                <h1>${title}</h1>
                <p>تاريخ الطباعة: ${app.formatDate(new Date())}</p>
            </div>
            ${table.outerHTML}
        `;

        app.printContent(printContent);
    }
}

// إنشاء مثيل AppCore
const appCore = new AppCore();
