🚀 تكنوفلاش - نظام إدارة نقاط البيع العربي
===============================================

📋 تعليمات الاستخدام السريع:

1️⃣ تشغيل النظام:
   - افتح ملف index.html في المتصفح
   - أو افتح test.html لاختبار النظام أولاً

2️⃣ تسجيل الدخول:
   - تم إلغاء تسجيل الدخول افتراضياً
   - يفتح النظام مباشرة بدون كلمة مرور
   - يمكن تفعيل تسجيل الدخول من الإعدادات

3️⃣ الاستخدام الأساسي:

   📊 لوحة المعلومات:
   - عرض الإحصائيات العامة
   - مراقبة المبيعات والمخزون
   - التنبيهات المهمة

   📦 إدارة المنتجات:
   - إضافة منتجات جديدة
   - تعديل الأسعار والكميات
   - تصنيف المنتجات

   🛒 المبيعات:
   - واجهة بيع سريعة
   - اختيار العملاء
   - طباعة الفواتير

   👥 إدارة العملاء:
   - إضافة عملاء جدد
   - متابعة الأرصدة
   - كشوف الحساب

4️⃣ الإعدادات المهمة:
   - بيانات الشركة (الإعدادات)
   - نسبة الضريبة (افتراضي 15%)
   - إعدادات الأمان:
     * تفعيل/إلغاء تسجيل الدخول
     * تغيير كلمة المرور
     * كلمة المرور الافتراضية: 123

5️⃣ النسخ الاحتياطي:
   - تصدير البيانات بانتظام
   - حفظ ملف JSON في مكان آمن
   - استيراد البيانات عند الحاجة

🔧 حل المشاكل الشائعة:

❌ المشكلة: النظام لا يفتح
✅ الحل: 
   - تأكد من تمكين JavaScript
   - جرب متصفح آخر
   - افتح test.html للتشخيص

❌ المشكلة: لا تظهر البيانات
✅ الحل:
   - امسح cache المتصفح
   - تأكد من localStorage
   - أعد تحميل الصفحة

❌ المشكلة: تريد تفعيل تسجيل الدخول
✅ الحل:
   - اذهب إلى الإعدادات
   - فعّل مفتاح "تسجيل الدخول"
   - أعد تحميل الصفحة

❌ المشكلة: مشاكل الطباعة
✅ الحل:
   - تحقق من إعدادات الطابعة
   - جرب متصفح آخر
   - استخدم معاينة الطباعة

📞 الدعم الفني:
   - البريد: <EMAIL>
   - الموقع: technoflash.com

🎯 نصائح للاستخدام الأمثل:

1. ابدأ بإعداد بيانات الشركة
2. أضف الفئات قبل المنتجات
3. أضف العملاء الأساسيين
4. اعمل نسخة احتياطية يومياً
5. راجع التقارير بانتظام

✨ ميزات متقدمة:

🔍 البحث العام: Ctrl+F
🖨️ طباعة سريعة: Ctrl+P
💾 حفظ: Ctrl+S
🔄 تحديث: Ctrl+R

📱 للتحويل لتطبيق سطح مكتب:
   npm install
   npm run build

🎉 استمتع باستخدام تكنوفلاش!
