// تكنوفلاش - نظام المبيعات
class SalesManager {
    constructor() {
        this.cart = [];
        this.currentCustomer = null;
        this.currentSale = null;
        this.paymentMethod = 'cash';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadGuestCustomer();
    }

    // تحميل صفحة المبيعات
    load() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getHTML();
        
        // تحميل البيانات
        this.loadData();
        
        // إعداد الأحداث
        this.setupEvents();
        
        // تحميل العميل الافتراضي
        this.loadGuestCustomer();
    }

    // الحصول على HTML صفحة المبيعات
    getHTML() {
        return `
            <div class="sales-page fade-in">
                <!-- رأس الصفحة -->
                <div class="page-header">
                    <div class="page-title">
                        <h1><i class="fas fa-shopping-cart"></i> نقطة البيع</h1>
                        <p>إدارة المبيعات والفواتير</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-success" onclick="salesManager.newSale()">
                            <i class="fas fa-plus"></i>
                            بيع جديد
                        </button>
                        <button class="btn btn-secondary" onclick="salesManager.showSalesHistory()">
                            <i class="fas fa-history"></i>
                            سجل المبيعات
                        </button>
                    </div>
                </div>

                <!-- واجهة البيع -->
                <div class="sales-interface">
                    <!-- قسم المنتجات -->
                    <div class="products-section">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">المنتجات</h3>
                                <div class="search-box">
                                    <input type="text" id="productSearch" placeholder="البحث بالاسم أو الباركود..." 
                                           onkeyup="salesManager.searchProducts(this.value)">
                                    <i class="fas fa-search"></i>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- فلاتر سريعة -->
                                <div class="quick-filters">
                                    <button class="filter-btn active" onclick="salesManager.filterProducts('all')">
                                        الكل
                                    </button>
                                    <div id="categoryFilters">
                                        <!-- سيتم تحميل فلاتر الفئات هنا -->
                                    </div>
                                </div>
                                
                                <!-- شبكة المنتجات -->
                                <div id="productsGrid" class="products-grid">
                                    <!-- سيتم تحميل المنتجات هنا -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قسم سلة المشتريات -->
                    <div class="cart-section">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">سلة المشتريات</h3>
                                <button class="btn btn-sm btn-danger" onclick="salesManager.clearCart()">
                                    <i class="fas fa-trash"></i>
                                    مسح الكل
                                </button>
                            </div>
                            <div class="card-body">
                                <!-- معلومات العميل -->
                                <div class="customer-section">
                                    <div class="customer-info">
                                        <label>العميل:</label>
                                        <div class="customer-selector">
                                            <select id="customerSelect" onchange="salesManager.selectCustomer(this.value)">
                                                <option value="">اختر العميل</option>
                                            </select>
                                            <button class="btn btn-sm btn-primary" onclick="salesManager.showAddCustomerModal()">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- عناصر السلة -->
                                <div id="cartItems" class="cart-items">
                                    <div class="empty-cart">
                                        <i class="fas fa-shopping-cart"></i>
                                        <p>السلة فارغة</p>
                                        <small>اختر المنتجات لإضافتها</small>
                                    </div>
                                </div>

                                <!-- ملخص الفاتورة -->
                                <div id="invoiceSummary" class="invoice-summary hidden">
                                    <div class="summary-row">
                                        <span>المجموع الفرعي:</span>
                                        <span id="subtotal">٠.٠٠ ريال</span>
                                    </div>
                                    <div class="summary-row">
                                        <span>الخصم:</span>
                                        <div class="discount-input">
                                            <input type="text" id="discountAmount" value="٠" 
                                                   onchange="salesManager.updateDiscount(this.value)">
                                            <span>ريال</span>
                                        </div>
                                    </div>
                                    <div class="summary-row">
                                        <span>الضريبة (١٥%):</span>
                                        <span id="taxAmount">٠.٠٠ ريال</span>
                                    </div>
                                    <div class="summary-row total">
                                        <span>الإجمالي:</span>
                                        <span id="totalAmount">٠.٠٠ ريال</span>
                                    </div>
                                </div>

                                <!-- طرق الدفع -->
                                <div id="paymentMethods" class="payment-methods hidden">
                                    <h4>طريقة الدفع:</h4>
                                    <div class="payment-options">
                                        <label class="payment-option">
                                            <input type="radio" name="paymentMethod" value="cash" checked 
                                                   onchange="salesManager.setPaymentMethod(this.value)">
                                            <span class="option-content">
                                                <i class="fas fa-money-bill-wave"></i>
                                                نقداً
                                            </span>
                                        </label>
                                        <label class="payment-option">
                                            <input type="radio" name="paymentMethod" value="credit" 
                                                   onchange="salesManager.setPaymentMethod(this.value)">
                                            <span class="option-content">
                                                <i class="fas fa-credit-card"></i>
                                                آجل
                                            </span>
                                        </label>
                                    </div>
                                </div>

                                <!-- أزرار الإجراءات -->
                                <div id="actionButtons" class="action-buttons hidden">
                                    <button class="btn btn-success btn-lg" onclick="salesManager.completeSale()">
                                        <i class="fas fa-check"></i>
                                        إتمام البيع
                                    </button>
                                    <button class="btn btn-secondary" onclick="salesManager.saveDraft()">
                                        <i class="fas fa-save"></i>
                                        حفظ مسودة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نافذة إضافة عميل سريع -->
            <div id="quickCustomerModal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>إضافة عميل جديد</h3>
                        <button class="modal-close" onclick="salesManager.hideAddCustomerModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <form id="quickCustomerForm" class="modal-body validate-form">
                        <div class="form-group">
                            <label for="customerName">اسم العميل *</label>
                            <input type="text" id="customerName" name="name" 
                                   class="validate-field" data-rules="required|min:2" 
                                   placeholder="أدخل اسم العميل">
                        </div>
                        <div class="form-group">
                            <label for="customerPhone">رقم الهاتف</label>
                            <input type="text" id="customerPhone" name="phone" 
                                   class="validate-field" data-rules="phone" 
                                   placeholder="أدخل رقم الهاتف">
                        </div>
                    </form>
                    <div class="modal-footer">
                        <button type="submit" form="quickCustomerForm" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ
                        </button>
                        <button class="btn btn-secondary" onclick="salesManager.hideAddCustomerModal()">
                            إلغاء
                        </button>
                    </div>
                </div>
            </div>

            <!-- نافذة سجل المبيعات -->
            <div id="salesHistoryModal" class="modal hidden">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3>سجل المبيعات</h3>
                        <button class="modal-close" onclick="salesManager.hideSalesHistory()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div id="salesHistoryContent">
                            <!-- سيتم تحميل سجل المبيعات هنا -->
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // الاستماع لأحداث قاعدة البيانات
        db.on('productUpdated', () => this.loadProducts());
        db.on('customerAdded', () => this.loadCustomers());
    }

    // تحميل البيانات
    loadData() {
        this.loadProducts();
        this.loadCustomers();
        this.loadCategories();
    }

    // إعداد الأحداث
    setupEvents() {
        // نموذج العميل السريع
        const quickCustomerForm = document.getElementById('quickCustomerForm');
        if (quickCustomerForm) {
            quickCustomerForm.addEventListener('submit', (e) => this.handleQuickCustomerSubmit(e));
        }

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (app.currentPage === 'sales') {
                this.handleKeyboardShortcuts(e);
            }
        });
    }

    // تحميل العميل الافتراضي
    loadGuestCustomer() {
        this.currentCustomer = db.getGuestCustomer();
    }

    // تحميل المنتجات
    loadProducts() {
        const products = db.getProducts().filter(p => p.active && p.quantity > 0);
        this.displayProducts(products);
    }

    // عرض المنتجات
    displayProducts(products) {
        const container = document.getElementById('productsGrid');
        
        if (products.length === 0) {
            container.innerHTML = `
                <div class="no-products">
                    <i class="fas fa-box-open"></i>
                    <p>لا توجد منتجات متاحة</p>
                </div>
            `;
            return;
        }

        let html = '';
        products.forEach(product => {
            const category = db.getCategoryById(product.categoryId);
            const categoryName = category ? category.name : 'عام';
            
            html += `
                <div class="product-card" onclick="salesManager.addToCart('${product.id}')">
                    <div class="product-info">
                        <h4>${product.name}</h4>
                        <p class="product-category">${categoryName}</p>
                        <div class="product-price">${app.formatCurrency(product.price)}</div>
                        <div class="product-stock">
                            متوفر: ${db.toArabicNumbers(product.quantity)} ${product.unit || 'قطعة'}
                        </div>
                    </div>
                    <div class="product-actions">
                        <button class="add-btn">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    // تحميل العملاء
    loadCustomers() {
        const customers = db.getCustomers();
        const customerSelect = document.getElementById('customerSelect');

        if (customerSelect) {
            customerSelect.innerHTML = '<option value="">اختر العميل</option>';
            customers.forEach(customer => {
                const selected = this.currentCustomer && this.currentCustomer.id === customer.id ? 'selected' : '';
                customerSelect.innerHTML += `<option value="${customer.id}" ${selected}>${customer.name}</option>`;
            });
        }
    }

    // تحميل الفئات
    loadCategories() {
        const categories = db.getCategories();
        const container = document.getElementById('categoryFilters');

        if (container) {
            let html = '';
            categories.forEach(category => {
                html += `
                    <button class="filter-btn" onclick="salesManager.filterProducts('${category.id}')">
                        ${category.name}
                    </button>
                `;
            });
            container.innerHTML = html;
        }
    }

    // البحث في المنتجات
    searchProducts(query) {
        if (!query.trim()) {
            this.loadProducts();
            return;
        }

        const products = db.searchProducts(query).filter(p => p.active && p.quantity > 0);
        this.displayProducts(products);
    }

    // فلترة المنتجات حسب الفئة
    filterProducts(categoryId) {
        // تحديث الأزرار النشطة
        document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');

        let products;
        if (categoryId === 'all') {
            products = db.getProducts().filter(p => p.active && p.quantity > 0);
        } else {
            products = db.getProducts().filter(p => p.active && p.quantity > 0 && p.categoryId === categoryId);
        }

        this.displayProducts(products);
    }

    // إضافة منتج إلى السلة
    addToCart(productId) {
        const product = db.getProductById(productId);
        if (!product) {
            app.showAlert('المنتج غير موجود', 'error');
            return;
        }

        if (product.quantity <= 0) {
            app.showAlert('المنتج غير متوفر في المخزون', 'warning');
            return;
        }

        // البحث عن المنتج في السلة
        const existingItem = this.cart.find(item => item.productId === productId);

        if (existingItem) {
            if (existingItem.quantity >= product.quantity) {
                app.showAlert('لا يمكن إضافة كمية أكثر من المتوفر', 'warning');
                return;
            }
            existingItem.quantity++;
        } else {
            this.cart.push({
                productId: productId,
                name: product.name,
                price: product.price,
                quantity: 1,
                unit: product.unit || 'قطعة'
            });
        }

        this.updateCartDisplay();
        this.updateInvoiceSummary();
    }

    // إزالة منتج من السلة
    removeFromCart(productId) {
        this.cart = this.cart.filter(item => item.productId !== productId);
        this.updateCartDisplay();
        this.updateInvoiceSummary();
    }

    // تحديث كمية منتج في السلة
    updateCartQuantity(productId, quantity) {
        const item = this.cart.find(item => item.productId === productId);
        if (!item) return;

        const product = db.getProductById(productId);
        if (quantity > product.quantity) {
            app.showAlert('الكمية المطلوبة أكثر من المتوفر', 'warning');
            return;
        }

        if (quantity <= 0) {
            this.removeFromCart(productId);
        } else {
            item.quantity = quantity;
            this.updateCartDisplay();
            this.updateInvoiceSummary();
        }
    }

    // تحديث عرض السلة
    updateCartDisplay() {
        const container = document.getElementById('cartItems');

        if (this.cart.length === 0) {
            container.innerHTML = `
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <p>السلة فارغة</p>
                    <small>اختر المنتجات لإضافتها</small>
                </div>
            `;

            // إخفاء الأقسام الأخرى
            document.getElementById('invoiceSummary').classList.add('hidden');
            document.getElementById('paymentMethods').classList.add('hidden');
            document.getElementById('actionButtons').classList.add('hidden');
            return;
        }

        let html = '';
        this.cart.forEach(item => {
            const total = item.price * item.quantity;
            html += `
                <div class="cart-item">
                    <div class="item-info">
                        <h4>${item.name}</h4>
                        <div class="item-price">${app.formatCurrency(item.price)} / ${item.unit}</div>
                    </div>
                    <div class="item-controls">
                        <div class="quantity-controls">
                            <button class="qty-btn" onclick="salesManager.updateCartQuantity('${item.productId}', ${item.quantity - 1})">
                                <i class="fas fa-minus"></i>
                            </button>
                            <span class="quantity">${db.toArabicNumbers(item.quantity)}</span>
                            <button class="qty-btn" onclick="salesManager.updateCartQuantity('${item.productId}', ${item.quantity + 1})">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <div class="item-total">${app.formatCurrency(total)}</div>
                        <button class="remove-btn" onclick="salesManager.removeFromCart('${item.productId}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;

        // إظهار الأقسام الأخرى
        document.getElementById('invoiceSummary').classList.remove('hidden');
        document.getElementById('paymentMethods').classList.remove('hidden');
        document.getElementById('actionButtons').classList.remove('hidden');
    }

    // تحديث ملخص الفاتورة
    updateInvoiceSummary() {
        const subtotal = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const discountAmount = app.parseNumber(document.getElementById('discountAmount')?.value || 0);
        const settings = db.getSettings();
        const taxRate = settings.taxRate / 100;
        const taxableAmount = subtotal - discountAmount;
        const taxAmount = taxableAmount * taxRate;
        const total = taxableAmount + taxAmount;

        document.getElementById('subtotal').textContent = app.formatCurrency(subtotal);
        document.getElementById('taxAmount').textContent = app.formatCurrency(taxAmount);
        document.getElementById('totalAmount').textContent = app.formatCurrency(total);
    }

    // تحديث الخصم
    updateDiscount(value) {
        const discountAmount = app.parseNumber(value);
        if (discountAmount < 0) {
            document.getElementById('discountAmount').value = '٠';
            return;
        }

        this.updateInvoiceSummary();
    }

    // مسح السلة
    clearCart() {
        if (this.cart.length === 0) return;

        app.showConfirm(
            'مسح السلة',
            'هل أنت متأكد من مسح جميع العناصر من السلة؟',
            () => {
                this.cart = [];
                this.updateCartDisplay();
                this.updateInvoiceSummary();
            }
        );
    }

    // اختيار العميل
    selectCustomer(customerId) {
        if (customerId) {
            this.currentCustomer = db.getCustomerById(customerId);
        } else {
            this.currentCustomer = db.getGuestCustomer();
        }
    }

    // تعيين طريقة الدفع
    setPaymentMethod(method) {
        this.paymentMethod = method;
    }

    // إتمام البيع
    completeSale() {
        if (this.cart.length === 0) {
            app.showAlert('السلة فارغة', 'warning');
            return;
        }

        if (!this.currentCustomer) {
            app.showAlert('يجب اختيار العميل', 'warning');
            return;
        }

        // حساب المبالغ
        const subtotal = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const discountAmount = app.parseNumber(document.getElementById('discountAmount')?.value || 0);
        const settings = db.getSettings();
        const taxRate = settings.taxRate / 100;
        const taxableAmount = subtotal - discountAmount;
        const taxAmount = taxableAmount * taxRate;
        const total = taxableAmount + taxAmount;

        // إنشاء بيانات البيع
        const saleData = {
            customerId: this.currentCustomer.id,
            items: [...this.cart],
            subtotal: subtotal,
            discount: discountAmount,
            tax: taxAmount,
            total: total,
            paymentMethod: this.paymentMethod,
            status: 'completed',
            createdAt: new Date().toISOString()
        };

        // حفظ البيع
        const sale = db.addSale(saleData);
        if (!sale) {
            app.showAlert('حدث خطأ أثناء حفظ البيع', 'error');
            return;
        }

        // تحديث المخزون
        this.updateInventory();

        // تحديث رصيد العميل إذا كان الدفع آجل
        if (this.paymentMethod === 'credit' && !this.currentCustomer.isGuest) {
            const newBalance = (this.currentCustomer.balance || 0) - total;
            db.updateCustomer(this.currentCustomer.id, { balance: newBalance });
        }

        // طباعة الفاتورة
        this.printInvoice(sale);

        // إعادة تعيين السلة
        this.newSale();

        app.showAlert('تم إتمام البيع بنجاح', 'success');
    }

    // تحديث المخزون
    updateInventory() {
        this.cart.forEach(item => {
            const product = db.getProductById(item.productId);
            if (product) {
                const newQuantity = product.quantity - item.quantity;
                db.updateProduct(item.productId, { quantity: Math.max(0, newQuantity) });
            }
        });
    }

    // طباعة الفاتورة
    printInvoice(sale) {
        const settings = db.getSettings();
        const customer = this.currentCustomer;

        const invoiceContent = `
            <div class="invoice">
                <div class="invoice-header">
                    <h1>${settings.companyName}</h1>
                    <div class="company-info">
                        <p>${settings.companyAddress}</p>
                        <p>هاتف: ${settings.companyPhone}</p>
                        <p>بريد إلكتروني: ${settings.companyEmail}</p>
                    </div>
                </div>

                <div class="invoice-details">
                    <div class="section">
                        <h3>بيانات الفاتورة</h3>
                        <p><strong>رقم الفاتورة:</strong> ${sale.id}</p>
                        <p><strong>التاريخ:</strong> ${app.formatDate(sale.createdAt)}</p>
                        <p><strong>الوقت:</strong> ${app.formatTime(sale.createdAt)}</p>
                    </div>

                    <div class="section">
                        <h3>بيانات العميل</h3>
                        <p><strong>الاسم:</strong> ${customer.name}</p>
                        ${customer.phone ? `<p><strong>الهاتف:</strong> ${customer.phone}</p>` : ''}
                        ${customer.address ? `<p><strong>العنوان:</strong> ${customer.address}</p>` : ''}
                    </div>
                </div>

                <div class="invoice-items">
                    <table>
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${sale.items.map(item => `
                                <tr>
                                    <td>${item.name}</td>
                                    <td>${app.formatCurrency(item.price)}</td>
                                    <td>${db.toArabicNumbers(item.quantity)} ${item.unit}</td>
                                    <td>${app.formatCurrency(item.price * item.quantity)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>

                <div class="invoice-total">
                    <table>
                        <tr>
                            <td>المجموع الفرعي:</td>
                            <td>${app.formatCurrency(sale.subtotal)}</td>
                        </tr>
                        ${sale.discount > 0 ? `
                            <tr>
                                <td>الخصم:</td>
                                <td>-${app.formatCurrency(sale.discount)}</td>
                            </tr>
                        ` : ''}
                        <tr>
                            <td>الضريبة (${db.toArabicNumbers(settings.taxRate)}%):</td>
                            <td>${app.formatCurrency(sale.tax)}</td>
                        </tr>
                        <tr class="total-row">
                            <td>الإجمالي:</td>
                            <td>${app.formatCurrency(sale.total)}</td>
                        </tr>
                    </table>
                </div>

                <div class="invoice-footer">
                    <p>طريقة الدفع: ${this.paymentMethod === 'cash' ? 'نقداً' : 'آجل'}</p>
                    <p>شكراً لتعاملكم معنا</p>
                </div>
            </div>
        `;

        app.printContent(invoiceContent);
    }

    // حفظ مسودة
    saveDraft() {
        if (this.cart.length === 0) {
            app.showAlert('السلة فارغة', 'warning');
            return;
        }

        const draftData = {
            cart: [...this.cart],
            customerId: this.currentCustomer?.id,
            paymentMethod: this.paymentMethod,
            discount: app.parseNumber(document.getElementById('discountAmount')?.value || 0),
            timestamp: new Date().toISOString()
        };

        localStorage.setItem('technoflash_sale_draft', JSON.stringify(draftData));
        app.showAlert('تم حفظ المسودة', 'success');
    }

    // تحميل مسودة
    loadDraft() {
        const draftData = localStorage.getItem('technoflash_sale_draft');
        if (!draftData) return false;

        try {
            const draft = JSON.parse(draftData);
            this.cart = draft.cart || [];
            this.paymentMethod = draft.paymentMethod || 'cash';

            if (draft.customerId) {
                this.currentCustomer = db.getCustomerById(draft.customerId);
            }

            this.updateCartDisplay();
            this.updateInvoiceSummary();
            this.loadCustomers();

            if (draft.discount) {
                document.getElementById('discountAmount').value = draft.discount;
            }

            // تحديد طريقة الدفع
            const paymentRadio = document.querySelector(`input[name="paymentMethod"][value="${this.paymentMethod}"]`);
            if (paymentRadio) {
                paymentRadio.checked = true;
            }

            localStorage.removeItem('technoflash_sale_draft');
            app.showAlert('تم تحميل المسودة', 'info');
            return true;
        } catch (error) {
            console.error('خطأ في تحميل المسودة:', error);
            return false;
        }
    }

    // بيع جديد
    newSale() {
        this.cart = [];
        this.currentCustomer = db.getGuestCustomer();
        this.paymentMethod = 'cash';

        // إعادة تعيين النموذج
        if (document.getElementById('customerSelect')) {
            document.getElementById('customerSelect').value = '';
        }
        if (document.getElementById('discountAmount')) {
            document.getElementById('discountAmount').value = '٠';
        }

        // تحديد الدفع نقداً
        const cashRadio = document.querySelector('input[name="paymentMethod"][value="cash"]');
        if (cashRadio) {
            cashRadio.checked = true;
        }

        this.updateCartDisplay();
        this.updateInvoiceSummary();

        // محاولة تحميل مسودة محفوظة
        this.loadDraft();
    }

    // عرض نافذة إضافة عميل
    showAddCustomerModal() {
        document.getElementById('quickCustomerModal').classList.remove('hidden');
        document.getElementById('customerName').focus();
    }

    // إخفاء نافذة إضافة عميل
    hideAddCustomerModal() {
        document.getElementById('quickCustomerModal').classList.add('hidden');
        document.getElementById('quickCustomerForm').reset();
    }

    // معالجة إرسال نموذج العميل السريع
    handleQuickCustomerSubmit(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const customerData = {
            name: app.sanitizeText(formData.get('name')),
            phone: formData.get('phone') || '',
            email: '',
            address: '',
            balance: 0
        };

        const customer = db.addCustomer(customerData);
        if (customer) {
            this.currentCustomer = customer;
            this.loadCustomers();
            this.hideAddCustomerModal();
            app.showAlert('تم إضافة العميل بنجاح', 'success');
        } else {
            app.showAlert('حدث خطأ أثناء إضافة العميل', 'error');
        }
    }

    // عرض سجل المبيعات
    showSalesHistory() {
        const sales = db.getSales();
        this.displaySalesHistory(sales);
        document.getElementById('salesHistoryModal').classList.remove('hidden');
    }

    // إخفاء سجل المبيعات
    hideSalesHistory() {
        document.getElementById('salesHistoryModal').classList.add('hidden');
    }

    // عرض سجل المبيعات
    displaySalesHistory(sales) {
        const container = document.getElementById('salesHistoryContent');

        if (sales.length === 0) {
            container.innerHTML = `
                <div class="no-sales">
                    <i class="fas fa-receipt"></i>
                    <p>لا توجد مبيعات</p>
                </div>
            `;
            return;
        }

        const columns = [
            { key: 'id', title: 'رقم الفاتورة' },
            {
                key: 'customerId',
                title: 'العميل',
                format: (value) => {
                    const customer = db.getCustomerById(value);
                    return customer ? customer.name : 'غير محدد';
                }
            },
            {
                key: 'total',
                title: 'الإجمالي',
                format: (value) => app.formatCurrency(value)
            },
            {
                key: 'paymentMethod',
                title: 'طريقة الدفع',
                format: (value) => value === 'cash' ? 'نقداً' : 'آجل'
            },
            {
                key: 'createdAt',
                title: 'التاريخ',
                format: (value) => app.formatDate(value)
            },
            {
                key: 'actions',
                title: 'الإجراءات',
                format: (value, row) => `
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="salesManager.viewSale('${row.id}')" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="salesManager.printSale('${row.id}')" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                    </div>
                `
            }
        ];

        appCore.createSortableTable(sales, columns, 'salesHistoryContent');
    }

    // عرض تفاصيل البيع
    viewSale(saleId) {
        const sale = db.getSaleById(saleId);
        if (!sale) {
            app.showAlert('البيع غير موجود', 'error');
            return;
        }

        const customer = db.getCustomerById(sale.customerId);
        const customerName = customer ? customer.name : 'غير محدد';

        let content = `
            <div class="sale-details">
                <h2>تفاصيل البيع #${sale.id}</h2>
                <div class="sale-info">
                    <div class="info-row">
                        <strong>العميل:</strong> ${customerName}
                    </div>
                    <div class="info-row">
                        <strong>التاريخ:</strong> ${app.formatDate(sale.createdAt)}
                    </div>
                    <div class="info-row">
                        <strong>الوقت:</strong> ${app.formatTime(sale.createdAt)}
                    </div>
                    <div class="info-row">
                        <strong>طريقة الدفع:</strong> ${sale.paymentMethod === 'cash' ? 'نقداً' : 'آجل'}
                    </div>
                </div>

                <h3>المنتجات:</h3>
                <table class="table">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>السعر</th>
                            <th>الكمية</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        sale.items.forEach(item => {
            content += `
                <tr>
                    <td>${item.name}</td>
                    <td>${app.formatCurrency(item.price)}</td>
                    <td>${db.toArabicNumbers(item.quantity)} ${item.unit}</td>
                    <td>${app.formatCurrency(item.price * item.quantity)}</td>
                </tr>
            `;
        });

        content += `
                    </tbody>
                </table>

                <div class="sale-summary">
                    <div class="summary-row">
                        <span>المجموع الفرعي:</span>
                        <span>${app.formatCurrency(sale.subtotal)}</span>
                    </div>
        `;

        if (sale.discount > 0) {
            content += `
                    <div class="summary-row">
                        <span>الخصم:</span>
                        <span>-${app.formatCurrency(sale.discount)}</span>
                    </div>
            `;
        }

        content += `
                    <div class="summary-row">
                        <span>الضريبة:</span>
                        <span>${app.formatCurrency(sale.tax)}</span>
                    </div>
                    <div class="summary-row total">
                        <span>الإجمالي:</span>
                        <span>${app.formatCurrency(sale.total)}</span>
                    </div>
                </div>
            </div>
        `;

        app.printContent(content);
    }

    // طباعة البيع
    printSale(saleId) {
        const sale = db.getSaleById(saleId);
        if (!sale) {
            app.showAlert('البيع غير موجود', 'error');
            return;
        }

        // تعيين البيع الحالي مؤقتاً للطباعة
        const originalCustomer = this.currentCustomer;
        this.currentCustomer = db.getCustomerById(sale.customerId);

        this.printInvoice(sale);

        // استعادة العميل الأصلي
        this.currentCustomer = originalCustomer;
    }

    // اختصارات لوحة المفاتيح
    handleKeyboardShortcuts(e) {
        // F1 - بيع جديد
        if (e.key === 'F1') {
            e.preventDefault();
            this.newSale();
        }

        // F2 - إتمام البيع
        if (e.key === 'F2') {
            e.preventDefault();
            this.completeSale();
        }

        // F3 - مسح السلة
        if (e.key === 'F3') {
            e.preventDefault();
            this.clearCart();
        }

        // Escape - إلغاء العملية الحالية
        if (e.key === 'Escape') {
            if (!document.querySelector('.modal.hidden')) {
                this.hideAddCustomerModal();
                this.hideSalesHistory();
            }
        }
    }
}

// إنشاء مثيل إدارة المبيعات
const salesManager = new SalesManager();

// وظيفة تحميل صفحة المبيعات
function loadSales() {
    app.currentPage = 'sales';
    app.updateActiveNavItem('sales');
    salesManager.load();
}
