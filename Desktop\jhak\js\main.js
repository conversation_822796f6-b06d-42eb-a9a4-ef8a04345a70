// تكنوفلاش - الملف الرئيسي للتطبيق
class TechnoFlashApp {
    constructor() {
        this.currentUser = null;
        this.currentPage = 'dashboard';
        this.isLoggedIn = false;
        this.init();
    }

    // تهيئة التطبيق
    init() {
        this.setupEventListeners();
        this.checkLoginStatus();
        this.setupTheme();
        this.setupNotifications();
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // تسجيل الدخول
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // تبديل الثيم
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // قائمة المستخدم
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userDropdown = document.getElementById('userDropdown');
        if (userMenuBtn && userDropdown) {
            userMenuBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                userDropdown.classList.toggle('hidden');
            });

            // إغلاق القائمة عند النقر خارجها
            document.addEventListener('click', () => {
                userDropdown.classList.add('hidden');
            });
        }

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));

        // إعداد الشريط الجانبي للهواتف
        this.setupMobileSidebar();
    }

    // التحقق من حالة تسجيل الدخول
    checkLoginStatus() {
        const settings = db.getSettings() || {};

        // التحقق من إعداد تسجيل الدخول
        if (settings.loginRequired === false) {
            // الدخول المباشر بدون كلمة مرور
            this.isLoggedIn = true;
            localStorage.setItem('technoflash_login_status', 'true');
            localStorage.setItem('technoflash_login_time', new Date().getTime().toString());
            this.showMainApp();
            this.loadDashboard();
            return;
        }

        // التحقق من حالة تسجيل الدخول العادية
        const loginStatus = localStorage.getItem('technoflash_login_status');
        const loginTime = localStorage.getItem('technoflash_login_time');

        if (loginStatus === 'true' && loginTime) {
            const now = new Date().getTime();
            const loginTimestamp = parseInt(loginTime);
            const hoursDiff = (now - loginTimestamp) / (1000 * 60 * 60);

            if (hoursDiff < 24) {
                this.isLoggedIn = true;
                this.showMainApp();
                this.loadDashboard();
            } else {
                this.logout();
            }
        } else {
            this.showLoginScreen();
        }
    }

    // معالجة تسجيل الدخول
    handleLogin(e) {
        e.preventDefault();
        const password = document.getElementById('password').value;
        const settings = db.getSettings();

        if (db.verifyPassword(password, settings.password)) {
            this.isLoggedIn = true;
            localStorage.setItem('technoflash_login_status', 'true');
            localStorage.setItem('technoflash_login_time', new Date().getTime().toString());

            this.showMainApp();
            this.loadDashboard();
            this.showAlert('مرحباً بك في تكنوفلاش!', 'success');
        } else {
            this.showAlert('كلمة المرور غير صحيحة', 'error');
            document.getElementById('password').value = '';
            document.getElementById('password').focus();
        }
    }

    // تسجيل الخروج
    logout() {
        this.isLoggedIn = false;
        this.currentUser = null;
        localStorage.removeItem('technoflash_login_status');
        localStorage.removeItem('technoflash_login_time');
        
        this.showLoginScreen();
        this.showAlert('تم تسجيل الخروج بنجاح', 'info');
    }

    // عرض شاشة تسجيل الدخول
    showLoginScreen() {
        document.getElementById('loginScreen').classList.remove('hidden');
        document.getElementById('mainApp').classList.add('hidden');
        document.getElementById('password').focus();
    }

    // عرض التطبيق الرئيسي
    showMainApp() {
        document.getElementById('loginScreen').classList.add('hidden');
        document.getElementById('mainApp').classList.remove('hidden');
    }

    // إعداد الثيم
    setupTheme() {
        const savedTheme = localStorage.getItem('technoflash_theme') || 'light';
        this.setTheme(savedTheme);
    }

    // تبديل الثيم
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }

    // تعيين الثيم
    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('technoflash_theme', theme);
        
        const themeIcon = document.querySelector('#themeToggle i');
        if (themeIcon) {
            themeIcon.className = theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    // إعداد الإشعارات
    setupNotifications() {
        // التحقق من المخزون المنخفض
        this.checkLowStock();
        
        // التحقق من الديون المتأخرة
        this.checkOverdueDebts();
        
        // إعداد التحقق الدوري
        setInterval(() => {
            this.checkLowStock();
            this.checkOverdueDebts();
        }, 300000); // كل 5 دقائق
    }

    // التحقق من المخزون المنخفض
    checkLowStock() {
        const lowStockProducts = db.getLowStockProducts(10);
        if (lowStockProducts.length > 0) {
            const message = `تحذير: ${db.toArabicNumbers(lowStockProducts.length)} منتج منخفض المخزون`;
            this.showNotification(message, 'warning');
        }
    }

    // التحقق من الديون المتأخرة
    checkOverdueDebts() {
        // سيتم تنفيذها في ملف الديون
    }

    // عرض الإشعارات
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        // إضافة الإشعار إلى الصفحة
        document.body.appendChild(notification);
        
        // إزالة الإشعار تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    // الحصول على أيقونة الإشعار
    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // اختصارات لوحة المفاتيح
    handleKeyboardShortcuts(e) {
        if (!this.isLoggedIn) return;
        
        // Ctrl/Cmd + مفاتيح أخرى
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case '1':
                    e.preventDefault();
                    this.showDashboard();
                    break;
                case '2':
                    e.preventDefault();
                    this.showProducts();
                    break;
                case '3':
                    e.preventDefault();
                    this.showSales();
                    break;
                case '4':
                    e.preventDefault();
                    this.showCustomers();
                    break;
                case 's':
                    e.preventDefault();
                    this.showSettings();
                    break;
            }
        }
        
        // مفتاح Escape لإغلاق النوافذ المنبثقة
        if (e.key === 'Escape') {
            this.hideAllModals();
        }
    }

    // إعداد الشريط الجانبي للهواتف
    setupMobileSidebar() {
        // إضافة زر القائمة للهواتف
        const navbar = document.querySelector('.top-navbar');
        if (navbar && window.innerWidth <= 768) {
            const menuBtn = document.createElement('button');
            menuBtn.className = 'mobile-menu-btn';
            menuBtn.innerHTML = '<i class="fas fa-bars"></i>';
            menuBtn.addEventListener('click', () => {
                document.querySelector('.sidebar').classList.toggle('active');
            });
            navbar.insertBefore(menuBtn, navbar.firstChild);
        }
    }

    // تحميل الصفحات
    loadDashboard() {
        this.currentPage = 'dashboard';
        this.updateActiveNavItem('dashboard');

        // إضافة تأخير بسيط للتأكد من تحميل جميع الملفات
        setTimeout(() => {
            if (typeof loadDashboard === 'function') {
                loadDashboard();
            } else {
                // تحميل محتوى افتراضي
                const contentArea = document.getElementById('contentArea');
                if (contentArea) {
                    contentArea.innerHTML = `
                        <div class="dashboard fade-in">
                            <h1>مرحباً بك في تكنوفلاش</h1>
                            <p>نظام إدارة نقاط البيع العربي</p>
                            <div class="card">
                                <div class="card-body">
                                    <p>جاري تحميل لوحة المعلومات...</p>
                                    <button class="btn btn-primary" onclick="location.reload()">إعادة تحميل</button>
                                </div>
                            </div>
                        </div>
                    `;
                }
            }
        }, 100);
    }

    // تحديث العنصر النشط في التنقل
    updateActiveNavItem(page) {
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // البحث عن العنصر النشط بطرق مختلفة
        let activeItem = document.querySelector(`[onclick="show${page.charAt(0).toUpperCase() + page.slice(1)}()"]`)?.closest('.nav-item');

        // إذا لم يجد، جرب البحث بطريقة أخرى
        if (!activeItem) {
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                const onclick = link.getAttribute('onclick');
                if (onclick && onclick.includes(page)) {
                    activeItem = link.closest('.nav-item');
                }
            });
        }

        if (activeItem) {
            activeItem.classList.add('active');
        }
    }

    // عرض رسالة تأكيد
    showConfirm(title, message, callback) {
        document.getElementById('confirmTitle').textContent = title;
        document.getElementById('confirmMessage').textContent = message;
        document.getElementById('confirmModal').classList.remove('hidden');
        
        const confirmBtn = document.getElementById('confirmBtn');
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
        
        newConfirmBtn.addEventListener('click', () => {
            this.hideConfirmModal();
            if (callback) callback();
        });
    }

    // إخفاء نافذة التأكيد
    hideConfirmModal() {
        document.getElementById('confirmModal').classList.add('hidden');
    }

    // عرض رسالة تنبيه
    showAlert(message, type = 'info') {
        const alertModal = document.getElementById('alertModal');
        const alertTitle = document.getElementById('alertTitle');
        const alertMessage = document.getElementById('alertMessage');
        
        // تعيين العنوان حسب النوع
        const titles = {
            success: 'نجح',
            error: 'خطأ',
            warning: 'تحذير',
            info: 'معلومات'
        };
        
        alertTitle.textContent = titles[type] || 'معلومات';
        alertMessage.textContent = message;
        alertModal.classList.remove('hidden');
        
        // إغلاق تلقائي بعد 3 ثوان للرسائل الناجحة
        if (type === 'success') {
            setTimeout(() => {
                this.hideAlertModal();
            }, 3000);
        }
    }

    // إخفاء نافذة التنبيه
    hideAlertModal() {
        document.getElementById('alertModal').classList.add('hidden');
    }

    // إخفاء جميع النوافذ المنبثقة
    hideAllModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.add('hidden');
        });
    }

    // تنسيق الأرقام للعرض
    formatNumber(number) {
        return db.toArabicNumbers(parseFloat(number).toLocaleString('ar-SA'));
    }

    // تنسيق العملة للعرض
    formatCurrency(amount) {
        return db.formatCurrency(amount);
    }

    // تنسيق التاريخ للعرض
    formatDate(date) {
        return db.formatDate(date);
    }

    // تنسيق الوقت للعرض
    formatTime(date) {
        return db.formatTime(date);
    }

    // التحقق من صحة البريد الإلكتروني
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // التحقق من صحة رقم الهاتف
    isValidPhone(phone) {
        const phoneRegex = /^[0-9٠-٩+\-\s()]+$/;
        return phoneRegex.test(phone) && phone.length >= 10;
    }

    // تنظيف النص من المسافات الزائدة
    sanitizeText(text) {
        return text.trim().replace(/\s+/g, ' ');
    }

    // تحويل النص إلى رقم
    parseNumber(text) {
        const cleanText = db.fromArabicNumbers(text).replace(/[^\d.-]/g, '');
        return parseFloat(cleanText) || 0;
    }

    // إنشاء عنصر HTML
    createElement(tag, className, innerHTML) {
        const element = document.createElement(tag);
        if (className) element.className = className;
        if (innerHTML) element.innerHTML = innerHTML;
        return element;
    }

    // إضافة تأثير التحميل
    showLoading(element) {
        element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
        element.disabled = true;
    }

    // إزالة تأثير التحميل
    hideLoading(element, originalText) {
        element.innerHTML = originalText;
        element.disabled = false;
    }

    // طباعة المحتوى
    printContent(content) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>طباعة - تكنوفلاش</title>
                <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
                <link rel="stylesheet" href="css/print.css">
                <style>
                    body { font-family: 'Cairo', sans-serif; direction: rtl; }
                </style>
            </head>
            <body>
                ${content}
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.focus();
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 250);
    }
}

// إنشاء مثيل التطبيق
const app = new TechnoFlashApp();

// وظائف عامة للاستخدام في HTML
function showDashboard() { if (typeof loadDashboard === 'function') loadDashboard(); }
function showProducts() { if (typeof loadProducts === 'function') loadProducts(); }
function showSales() { if (typeof loadSales === 'function') loadSales(); }
function showCustomers() { if (typeof loadCustomers === 'function') loadCustomers(); }
function showSuppliers() { if (typeof loadSuppliers === 'function') loadSuppliers(); }
function showPurchases() { if (typeof loadPurchases === 'function') loadPurchases(); }
function showDebts() { if (typeof loadDebts === 'function') loadDebts(); }
function showReports() { if (typeof loadReports === 'function') loadReports(); }
function showSettings() { if (typeof loadSettings === 'function') loadSettings(); }
function showBackup() { if (typeof loadBackup === 'function') loadBackup(); }

function logout() { if (typeof app !== 'undefined') app.logout(); }
function hideConfirmModal() { if (typeof app !== 'undefined') app.hideConfirmModal(); }
function hideAlertModal() { if (typeof app !== 'undefined') app.hideAlertModal(); }
