// تكنوفلاش - إدارة الموردين
class SuppliersManager {
    constructor() {
        this.currentSupplier = null;
        this.filteredSuppliers = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    load() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="suppliers-page fade-in">
                <div class="page-header">
                    <div class="page-title">
                        <h1><i class="fas fa-truck"></i> إدارة الموردين</h1>
                        <p>إدارة بيانات الموردين والمشتريات</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="suppliersManager.showAddSupplierModal()">
                            <i class="fas fa-plus"></i>
                            إضافة مورد
                        </button>
                    </div>
                </div>

                <div class="suppliers-toolbar">
                    <div class="search-box">
                        <input type="text" id="supplierSearch" placeholder="البحث في الموردين..." 
                               onkeyup="suppliersManager.searchSuppliers(this.value)">
                        <i class="fas fa-search"></i>
                    </div>
                </div>

                <div class="suppliers-table-container">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">قائمة الموردين</h3>
                        </div>
                        <div class="card-body">
                            <div id="suppliersTableContainer"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="supplierModal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="supplierModalTitle">إضافة مورد جديد</h3>
                        <button class="modal-close" onclick="suppliersManager.hideSupplierModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <form id="supplierForm" class="modal-body validate-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="supplierName">اسم المورد *</label>
                                <input type="text" id="supplierName" name="name" 
                                       class="validate-field" data-rules="required|min:2" 
                                       placeholder="أدخل اسم المورد">
                            </div>
                            <div class="form-group">
                                <label for="supplierPhone">رقم الهاتف</label>
                                <input type="text" id="supplierPhone" name="phone" 
                                       placeholder="أدخل رقم الهاتف">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="supplierEmail">البريد الإلكتروني</label>
                                <input type="email" id="supplierEmail" name="email" 
                                       placeholder="أدخل البريد الإلكتروني">
                            </div>
                            <div class="form-group">
                                <label for="supplierCompany">الشركة</label>
                                <input type="text" id="supplierCompany" name="company" 
                                       placeholder="اسم الشركة">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="supplierAddress">العنوان</label>
                            <textarea id="supplierAddress" name="address" 
                                      placeholder="أدخل العنوان"></textarea>
                        </div>
                    </form>
                    <div class="modal-footer">
                        <button type="submit" form="supplierForm" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ
                        </button>
                        <button class="btn btn-secondary" onclick="suppliersManager.hideSupplierModal()">
                            إلغاء
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        this.loadData();
        this.setupEvents();
    }

    setupEventListeners() {
        db.on('supplierAdded', () => this.refreshSuppliers());
        db.on('supplierUpdated', () => this.refreshSuppliers());
        db.on('supplierDeleted', () => this.refreshSuppliers());
    }

    loadData() {
        this.loadSuppliers();
    }

    setupEvents() {
        const supplierForm = document.getElementById('supplierForm');
        if (supplierForm) {
            supplierForm.addEventListener('submit', (e) => this.handleSupplierSubmit(e));
        }
    }

    loadSuppliers() {
        const suppliers = db.getSuppliers();
        this.filteredSuppliers = [...suppliers];
        this.displaySuppliers();
    }

    displaySuppliers() {
        const container = document.getElementById('suppliersTableContainer');
        
        if (this.filteredSuppliers.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-truck"></i>
                    <h3>لا توجد موردين</h3>
                    <p>ابدأ بإضافة مورد جديد</p>
                    <button class="btn btn-primary" onclick="suppliersManager.showAddSupplierModal()">
                        <i class="fas fa-plus"></i>
                        إضافة مورد
                    </button>
                </div>
            `;
            return;
        }

        const columns = [
            { key: 'name', title: 'اسم المورد' },
            { key: 'company', title: 'الشركة' },
            { key: 'phone', title: 'رقم الهاتف' },
            { key: 'email', title: 'البريد الإلكتروني' },
            { 
                key: 'createdAt', 
                title: 'تاريخ الإضافة',
                format: (value) => app.formatDate(value)
            },
            {
                key: 'actions',
                title: 'الإجراءات',
                format: (value, row) => `
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="suppliersManager.editSupplier('${row.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="suppliersManager.deleteSupplier('${row.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `
            }
        ];

        appCore.createSortableTable(this.filteredSuppliers, columns, 'suppliersTableContainer');
    }

    searchSuppliers(query) {
        const suppliers = db.getSuppliers();
        if (!query.trim()) {
            this.filteredSuppliers = suppliers;
        } else {
            const searchTerm = query.toLowerCase();
            this.filteredSuppliers = suppliers.filter(supplier => 
                supplier.name.toLowerCase().includes(searchTerm) ||
                supplier.company.toLowerCase().includes(searchTerm) ||
                supplier.phone.includes(query) ||
                supplier.email.toLowerCase().includes(searchTerm)
            );
        }
        this.displaySuppliers();
    }

    refreshSuppliers() {
        this.loadData();
        app.showAlert('تم تحديث قائمة الموردين', 'success');
    }

    showAddSupplierModal() {
        this.currentSupplier = null;
        document.getElementById('supplierModalTitle').textContent = 'إضافة مورد جديد';
        this.resetSupplierForm();
        document.getElementById('supplierModal').classList.remove('hidden');
        document.getElementById('supplierName').focus();
    }

    editSupplier(supplierId) {
        const supplier = db.getSupplierById(supplierId);
        if (!supplier) {
            app.showAlert('المورد غير موجود', 'error');
            return;
        }

        this.currentSupplier = supplier;
        document.getElementById('supplierModalTitle').textContent = 'تعديل المورد';
        this.fillSupplierForm(supplier);
        document.getElementById('supplierModal').classList.remove('hidden');
        document.getElementById('supplierName').focus();
    }

    deleteSupplier(supplierId) {
        const supplier = db.getSupplierById(supplierId);
        if (!supplier) {
            app.showAlert('المورد غير موجود', 'error');
            return;
        }

        app.showConfirm(
            'حذف المورد',
            `هل أنت متأكد من حذف المورد "${supplier.name}"؟`,
            () => {
                if (db.deleteSupplier(supplierId)) {
                    app.showAlert('تم حذف المورد بنجاح', 'success');
                } else {
                    app.showAlert('حدث خطأ أثناء حذف المورد', 'error');
                }
            }
        );
    }

    hideSupplierModal() {
        document.getElementById('supplierModal').classList.add('hidden');
        this.resetSupplierForm();
        this.currentSupplier = null;
    }

    resetSupplierForm() {
        document.getElementById('supplierForm').reset();
        document.querySelectorAll('.field-error').forEach(error => error.remove());
        document.querySelectorAll('.error').forEach(field => field.classList.remove('error'));
    }

    fillSupplierForm(supplier) {
        document.getElementById('supplierName').value = supplier.name;
        document.getElementById('supplierPhone').value = supplier.phone || '';
        document.getElementById('supplierEmail').value = supplier.email || '';
        document.getElementById('supplierCompany').value = supplier.company || '';
        document.getElementById('supplierAddress').value = supplier.address || '';
    }

    handleSupplierSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const supplierData = {
            name: app.sanitizeText(formData.get('name')),
            phone: formData.get('phone') || '',
            email: formData.get('email') || '',
            company: app.sanitizeText(formData.get('company')) || '',
            address: app.sanitizeText(formData.get('address')) || ''
        };

        if (this.currentSupplier) {
            if (db.updateSupplier(this.currentSupplier.id, supplierData)) {
                app.showAlert('تم تحديث المورد بنجاح', 'success');
                this.hideSupplierModal();
            } else {
                app.showAlert('حدث خطأ أثناء تحديث المورد', 'error');
            }
        } else {
            if (db.addSupplier(supplierData)) {
                app.showAlert('تم إضافة المورد بنجاح', 'success');
                this.hideSupplierModal();
            } else {
                app.showAlert('حدث خطأ أثناء إضافة المورد', 'error');
            }
        }
    }
}

const suppliersManager = new SuppliersManager();

function loadSuppliers() {
    app.currentPage = 'suppliers';
    app.updateActiveNavItem('suppliers');
    suppliersManager.load();
}
