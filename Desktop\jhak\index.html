<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تكنوفلاش - نظام إدارة نقاط البيع العربي</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/print.css" media="print">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- شاشة تسجيل الدخول -->
    <div id="loginScreen" class="login-screen">
        <div class="login-container">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-cash-register"></i>
                    <h1>تكنوفلاش</h1>
                    <p>نظام إدارة نقاط البيع العربي</p>
                </div>
            </div>
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" name="password" placeholder="أدخل كلمة المرور" required>
                </div>
                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
                <div class="login-footer">
                    <p>كلمة المرور الافتراضية: ١٢٣</p>
                </div>
            </form>
        </div>
    </div>

    <!-- التطبيق الرئيسي -->
    <div id="mainApp" class="main-app hidden">
        <!-- شريط التنقل العلوي -->
        <header class="top-navbar">
            <div class="navbar-brand">
                <i class="fas fa-cash-register"></i>
                <span>تكنوفلاش</span>
            </div>
            <div class="navbar-controls">
                <div class="theme-toggle">
                    <button id="themeToggle" class="theme-btn">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
                <div class="user-menu">
                    <button id="userMenuBtn" class="user-btn">
                        <i class="fas fa-user"></i>
                        <span>المدير</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div id="userDropdown" class="user-dropdown hidden">
                        <a href="#" onclick="showSettings()">
                            <i class="fas fa-cog"></i>
                            الإعدادات
                        </a>
                        <a href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i>
                            تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- الشريط الجانبي -->
        <aside class="sidebar">
            <nav class="sidebar-nav">
                <ul class="nav-menu">
                    <li class="nav-item active">
                        <a href="#" onclick="showDashboard()" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة المعلومات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" onclick="showProducts()" class="nav-link">
                            <i class="fas fa-box"></i>
                            <span>المنتجات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" onclick="showSales()" class="nav-link">
                            <i class="fas fa-shopping-cart"></i>
                            <span>المبيعات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" onclick="showCustomers()" class="nav-link">
                            <i class="fas fa-users"></i>
                            <span>العملاء</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" onclick="showSuppliers()" class="nav-link">
                            <i class="fas fa-truck"></i>
                            <span>الموردين</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" onclick="showPurchases()" class="nav-link">
                            <i class="fas fa-shopping-bag"></i>
                            <span>المشتريات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" onclick="showDebts()" class="nav-link">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>الديون والمدفوعات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" onclick="showReports()" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" onclick="showSettings()" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" onclick="showBackup()" class="nav-link">
                            <i class="fas fa-database"></i>
                            <span>النسخ الاحتياطي</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <div id="contentArea" class="content-area">
                <!-- سيتم تحميل المحتوى هنا ديناميكياً -->
            </div>
        </main>
    </div>

    <!-- نافذة التأكيد -->
    <div id="confirmModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="confirmTitle">تأكيد العملية</h3>
                <button class="modal-close" onclick="hideConfirmModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">هل أنت متأكد من هذه العملية؟</p>
            </div>
            <div class="modal-footer">
                <button id="confirmBtn" class="btn btn-danger">تأكيد</button>
                <button class="btn btn-secondary" onclick="hideConfirmModal()">إلغاء</button>
            </div>
        </div>
    </div>

    <!-- نافذة التنبيه -->
    <div id="alertModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="alertTitle">تنبيه</h3>
                <button class="modal-close" onclick="hideAlertModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p id="alertMessage">رسالة التنبيه</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="hideAlertModal()">موافق</button>
            </div>
        </div>
    </div>

    <!-- تحميل ملفات JavaScript -->
    <script src="js/database.js"></script>
    <script src="js/app.js"></script>
    <script src="js/main.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/products.js"></script>
    <script src="js/sales.js"></script>
    <script src="js/customers.js"></script>
    <script src="js/suppliers.js"></script>
    <script src="js/purchases.js"></script>
    <script src="js/debts.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/backup.js"></script>
    <script src="js/print.js"></script>
</body>
</html>
