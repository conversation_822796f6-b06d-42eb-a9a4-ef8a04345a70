/* تكنوفلاش - نظام إدارة نقاط البيع العربي */
/* الخطوط والمتغيرات الأساسية */
:root {
    /* الألوان الأساسية */
    --primary-color: #667eea;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-color: #f093fb;
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-color: #4facfe;
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-color: #ffecd2;
    --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --danger-color: #ff9a9e;
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    
    /* ألوان الخلفية */
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-card: #ffffff;
    --bg-sidebar: #2d3748;
    --bg-navbar: #ffffff;
    
    /* ألوان النص */
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --text-muted: #a0aec0;
    --text-white: #ffffff;
    
    /* الظلال */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    
    /* Neumorphism */
    --neu-shadow: 8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff;
    --neu-inset: inset 8px 8px 16px #d1d9e6, inset -8px -8px 16px #ffffff;
    
    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* الحدود */
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;
    
    /* الانتقالات */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* الثيم الداكن */
[data-theme="dark"] {
    --bg-primary: #1a202c;
    --bg-secondary: #2d3748;
    --bg-card: #2d3748;
    --bg-sidebar: #1a202c;
    --bg-navbar: #2d3748;
    
    --text-primary: #f7fafc;
    --text-secondary: #e2e8f0;
    --text-muted: #a0aec0;
    
    --neu-shadow: 8px 8px 16px #0f1419, -8px -8px 16px #252d3a;
    --neu-inset: inset 8px 8px 16px #0f1419, inset -8px -8px 16px #252d3a;
}

/* إعادة تعيين الأنماط */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    direction: rtl;
    overflow-x: hidden;
}

/* الأدوات المساعدة */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

/* شاشة تسجيل الدخول */
.login-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.login-container {
    background: var(--bg-card);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--neu-shadow);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-header .logo i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.login-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.login-header p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
}

.login-form {
    text-align: right;
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.form-group input {
    width: 100%;
    padding: var(--spacing-md);
    border: none;
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    box-shadow: var(--neu-inset);
    font-size: 1rem;
    color: var(--text-primary);
    transition: var(--transition);
}

.form-group input:focus {
    outline: none;
    box-shadow: var(--neu-inset), 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.login-btn {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--primary-gradient);
    color: var(--text-white);
    border: none;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-md);
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.login-btn i {
    margin-left: var(--spacing-sm);
}

.login-footer {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--text-muted);
}

.login-footer p {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* التطبيق الرئيسي */
.main-app {
    display: grid;
    grid-template-areas: 
        "navbar navbar"
        "sidebar content";
    grid-template-columns: 280px 1fr;
    grid-template-rows: 70px 1fr;
    height: 100vh;
}

/* شريط التنقل العلوي */
.top-navbar {
    grid-area: navbar;
    background: var(--bg-navbar);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-lg);
    z-index: 100;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.navbar-brand i {
    margin-left: var(--spacing-sm);
    font-size: 1.8rem;
}

.navbar-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.theme-btn {
    background: var(--bg-primary);
    border: none;
    border-radius: var(--border-radius);
    padding: var(--spacing-sm);
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--neu-shadow);
    color: var(--text-primary);
}

.theme-btn:hover {
    transform: translateY(-1px);
}

.user-menu {
    position: relative;
}

.user-btn {
    background: var(--bg-primary);
    border: none;
    border-radius: var(--border-radius);
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--neu-shadow);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.user-btn:hover {
    transform: translateY(-1px);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-sm);
    min-width: 200px;
    z-index: 1000;
}

.user-dropdown a {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.user-dropdown a:hover {
    background: var(--bg-primary);
}

/* الشريط الجانبي */
.sidebar {
    grid-area: sidebar;
    background: var(--bg-sidebar);
    overflow-y: auto;
    box-shadow: var(--shadow-md);
}

.sidebar-nav {
    padding: var(--spacing-lg) 0;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-muted);
    text-decoration: none;
    transition: var(--transition);
    border-right: 3px solid transparent;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-white);
}

.nav-item.active .nav-link {
    background: rgba(102, 126, 234, 0.2);
    color: var(--text-white);
    border-right-color: var(--primary-color);
}

.nav-link i {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

/* المحتوى الرئيسي */
.main-content {
    grid-area: content;
    overflow-y: auto;
    padding: var(--spacing-lg);
    background: var(--bg-primary);
}

.content-area {
    max-width: 1200px;
    margin: 0 auto;
}

/* البطاقات */
.card {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--neu-shadow);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--text-muted);
}

.card-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-title i {
    color: var(--primary-color);
}

/* الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    box-shadow: var(--shadow-sm);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--text-white);
}

.btn-secondary {
    background: var(--bg-primary);
    color: var(--text-primary);
    box-shadow: var(--neu-shadow);
}

.btn-success {
    background: var(--success-gradient);
    color: var(--text-white);
}

.btn-warning {
    background: var(--warning-gradient);
    color: var(--text-primary);
}

.btn-danger {
    background: var(--danger-gradient);
    color: var(--text-white);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.8rem;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1.1rem;
}

/* النماذج */
.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: none;
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    box-shadow: var(--neu-inset);
    font-size: 1rem;
    color: var(--text-primary);
    transition: var(--transition);
    font-family: 'Cairo', sans-serif;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    box-shadow: var(--neu-inset), 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* الجداول */
.table-container {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--neu-shadow);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--text-muted);
}

.table th {
    background: var(--bg-primary);
    font-weight: 600;
    color: var(--text-primary);
}

.table tbody tr:hover {
    background: var(--bg-primary);
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--text-muted);
}

.modal-header h3 {
    color: var(--text-primary);
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--text-muted);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .main-app {
        grid-template-areas: 
            "navbar"
            "content";
        grid-template-columns: 1fr;
        grid-template-rows: 70px 1fr;
    }
    
    .sidebar {
        position: fixed;
        top: 70px;
        right: -280px;
        width: 280px;
        height: calc(100vh - 70px);
        z-index: 999;
        transition: var(--transition);
    }
    
    .sidebar.active {
        right: 0;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .login-container {
        margin: var(--spacing-md);
        padding: var(--spacing-lg);
    }
}

/* الرسوم المتحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* أنماط الطباعة */
@media print {
    .sidebar,
    .top-navbar,
    .btn,
    .modal {
        display: none !important;
    }

    .main-content {
        grid-area: unset;
        padding: 0;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* تحسينات إضافية للتصميم */

/* بطاقات الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--neu-shadow);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.sales { background: var(--primary-gradient); }
.stat-icon.products { background: var(--success-gradient); }
.stat-icon.customers { background: var(--secondary-gradient); }
.stat-icon.profit { background: var(--warning-gradient); }
.stat-icon.warning { background: var(--danger-gradient); }

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-content p {
    color: var(--text-secondary);
    margin: 0;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-change.positive { color: #4caf50; }
.stat-change.negative { color: #f44336; }

/* شبكة المنتجات */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-md);
    max-height: 400px;
    overflow-y: auto;
}

.product-card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--neu-shadow);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.product-info h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: 1rem;
}

.product-category {
    color: var(--text-muted);
    font-size: 0.8rem;
    margin-bottom: var(--spacing-sm);
}

.product-price {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: var(--spacing-xs);
}

.product-stock {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.add-btn {
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: var(--spacing-sm);
    cursor: pointer;
    transition: var(--transition);
}

.add-btn:hover {
    transform: scale(1.1);
}

/* سلة المشتريات */
.cart-items {
    max-height: 300px;
    overflow-y: auto;
}

.cart-item {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-info h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: 0.9rem;
}

.item-price {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.item-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.qty-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 0.8rem;
}

.quantity {
    font-weight: 600;
    color: var(--text-primary);
    min-width: 30px;
    text-align: center;
}

.item-total {
    font-weight: 600;
    color: var(--primary-color);
    min-width: 80px;
    text-align: right;
}

.remove-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 0.8rem;
}

/* ملخص الفاتورة */
.invoice-summary {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin: var(--spacing-md) 0;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.summary-row.total {
    border-top: 2px solid var(--primary-color);
    padding-top: var(--spacing-sm);
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--primary-color);
}

.discount-input {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.discount-input input {
    width: 80px;
    padding: var(--spacing-xs);
    border: 1px solid var(--text-muted);
    border-radius: var(--border-radius);
    text-align: center;
}

/* طرق الدفع */
.payment-methods {
    margin: var(--spacing-md) 0;
}

.payment-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
}

.payment-option {
    display: block;
    cursor: pointer;
}

.payment-option input[type="radio"] {
    display: none;
}

.option-content {
    background: var(--bg-primary);
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    text-align: center;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
}

.payment-option input[type="radio"]:checked + .option-content {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.option-content i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

/* الحالات الفارغة */
.empty-state, .empty-cart, .no-products, .no-results {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.empty-state i, .empty-cart i, .no-products i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    color: var(--text-muted);
}

.empty-state h3, .empty-cart p {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

/* الفلاتر السريعة */
.quick-filters {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
}

.filter-btn {
    background: var(--bg-primary);
    border: 1px solid var(--text-muted);
    border-radius: var(--border-radius);
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-primary);
    font-size: 0.9rem;
}

.filter-btn:hover, .filter-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
}

.action-buttons .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.8rem;
}

/* الرصيد */
.balance.positive { color: #4caf50; }
.balance.negative { color: #f44336; }

/* حالة المخزون */
.low-stock {
    color: #ff9800;
    font-weight: 600;
}

/* الحالة */
.status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 600;
}

.status.active {
    background: #e8f5e8;
    color: #4caf50;
}

.status.inactive {
    background: #ffebee;
    color: #f44336;
}

/* الإشعارات */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-md);
    z-index: 10000;
    max-width: 300px;
    animation: slideIn 0.3s ease-out;
}

.notification-success { border-left: 4px solid #4caf50; }
.notification-error { border-left: 4px solid #f44336; }
.notification-warning { border-left: 4px solid #ff9800; }
.notification-info { border-left: 4px solid #2196f3; }

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.notification-close {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-muted);
    margin-right: auto;
}

/* البحث العام */
.global-search-box {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.search-container {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
}

.search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--text-muted);
}

.search-input-container {
    position: relative;
    padding: var(--spacing-lg);
}

.search-input-container input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-md) 40px;
    border: 1px solid var(--text-muted);
    border-radius: var(--border-radius);
    font-size: 1rem;
}

.search-input-container i {
    position: absolute;
    left: 25px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.search-results {
    max-height: 400px;
    overflow-y: auto;
    padding: var(--spacing-md);
}

.search-section h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    padding-bottom: var(--spacing-xs);
    border-bottom: 1px solid var(--text-muted);
}

.search-result-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.search-result-item:hover {
    background: var(--bg-primary);
}

.search-result-item i {
    color: var(--primary-color);
    width: 20px;
}

/* التحسينات المتجاوبة */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .payment-options {
        grid-template-columns: 1fr;
    }

    .quick-filters {
        justify-content: center;
    }

    .action-buttons {
        flex-direction: column;
    }

    .search-container {
        width: 95%;
        margin: var(--spacing-md);
    }
}

/* مفتاح التبديل (Switch) */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background: var(--primary-gradient);
}

input:focus + .slider {
    box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}
