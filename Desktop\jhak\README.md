# تكنوفلاش - نظام إدارة نقاط البيع العربي

## نظرة عامة

تكنوفلاش هو نظام إدارة نقاط بيع شامل ومجاني مصمم خصيصاً للشركات العربية. يوفر النظام واجهة عربية كاملة مع دعم RTL وجميع الميزات المطلوبة لإدارة المبيعات والمخزون والعملاء.

## الميزات الرئيسية

### 🔐 نظام تسجيل الدخول
- حماية بكلمة مرور (افتراضية: 123)
- إمكانية تغيير كلمة المرور
- حفظ حالة تسجيل الدخول

### 📊 لوحة المعلومات
- إحصائيات سريعة للمبيعات والمنتجات
- مؤشرات الأداء اليومي والشهري
- تنبيهات المخزون المنخفض
- رسوم بيانية بسيطة
- أرقام عربية هندية

### 📦 إدارة المنتجات
- إضافة/تعديل/حذف المنتجات
- تصنيف المنتجات حسب الفئات
- بحث وفلترة متقدمة
- تنبيهات نفاد المخزون
- تحديث الأسعار

### 🛒 نظام المبيعات
- واجهة بيع سريعة وسهلة
- سلة مشتريات تفاعلية
- حساب الضريبة تلقائياً
- طرق دفع متعددة (نقد/آجل)
- طباعة فواتير احترافية

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- تتبع الأرصدة والديون
- تاريخ المعاملات
- طباعة كشوف الحساب
- إدارة المدفوعات

### 🚚 إدارة الموردين
- معلومات الاتصال الكاملة
- تتبع المدفوعات
- فواتير المشتريات
- سندات الاستلام

### 💰 إدارة الديون والمدفوعات
- متابعة الديون المستحقة
- تسجيل المدفوعات
- طباعة إيصالات الاستلام
- تنبيهات الديون المتأخرة

### 📈 التقارير والإحصائيات
- تقارير المبيعات (يومي/شهري/سنوي)
- تقارير المخزون
- تقارير العملاء والديون
- تقارير الأرباح والخسائر

### ⚙️ الإعدادات
- بيانات الشركة
- نسبة الضريبة
- إعدادات الطباعة
- تغيير كلمة المرور
- ثيم مضيء/داكن

### 💾 النسخ الاحتياطي
- تصدير البيانات بصيغة JSON
- استيراد البيانات
- حفظ تلقائي
- استعادة البيانات

## التقنيات المستخدمة

- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والأنماط
- **JavaScript (Vanilla)** - المنطق والوظائف
- **localStorage** - حفظ البيانات محلياً
- **Font Awesome** - الأيقونات
- **Google Fonts (Cairo)** - الخطوط العربية

## متطلبات التشغيل

- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- لا يتطلب اتصال إنترنت للعمل
- لا يتطلب خادم أو قاعدة بيانات

## التثبيت والاستخدام

### 1. تحميل الملفات
```bash
git clone https://github.com/technoflash/pos-system.git
cd pos-system
```

### 2. فتح النظام

**الطريقة السريعة:**
- انقر نقراً مزدوجاً على `start.bat` (Windows)
- أو افتح ملف `index.html` في المتصفح مباشرة

**للتشخيص:**
- انقر نقراً مزدوجاً على `diagnose.bat` لاختبار النظام
- أو افتح `test.html` في المتصفح

**باستخدام خادم محلي:**
```bash
python -m http.server 8000
# ثم افتح http://localhost:8000
```

### 3. تسجيل الدخول
- كلمة المرور الافتراضية: `123`
- يمكن تغييرها من الإعدادات
- إذا لم يعمل النظام، جرب صفحة التشخيص أولاً

## هيكل الملفات

```
technoflash-pos/
├── index.html              # الصفحة الرئيسية
├── css/
│   ├── style.css          # الأنماط الرئيسية
│   └── print.css          # أنماط الطباعة
├── js/
│   ├── main.js            # المنطق الأساسي
│   ├── app.js             # وظائف التطبيق
│   ├── database.js        # إدارة قاعدة البيانات
│   ├── dashboard.js       # لوحة المعلومات
│   ├── products.js        # إدارة المنتجات
│   ├── sales.js           # نظام المبيعات
│   ├── customers.js       # إدارة العملاء
│   ├── suppliers.js       # إدارة الموردين
│   ├── purchases.js       # نظام المشتريات
│   ├── debts.js           # إدارة الديون
│   ├── reports.js         # التقارير
│   ├── settings.js        # الإعدادات
│   ├── backup.js          # النسخ الاحتياطي
│   └── print.js           # نظام الطباعة
└── README.md              # هذا الملف
```

## الاستخدام

### إضافة منتج جديد
1. اذهب إلى "المنتجات"
2. اضغط "إضافة منتج"
3. املأ البيانات المطلوبة
4. اضغط "حفظ"

### إجراء عملية بيع
1. اذهب إلى "المبيعات"
2. اختر المنتجات من الشبكة
3. حدد العميل وطريقة الدفع
4. اضغط "إتمام البيع"

### طباعة الفواتير
- يتم طباعة الفاتورة تلقائياً بعد إتمام البيع
- يمكن إعادة طباعة الفواتير من سجل المبيعات

## التخصيص

### تغيير بيانات الشركة
1. اذهب إلى "الإعدادات"
2. قسم "بيانات الشركة"
3. املأ البيانات المطلوبة
4. اضغط "حفظ"

### تغيير نسبة الضريبة
1. اذهب إلى "الإعدادات"
2. غير قيمة "نسبة الضريبة"
3. اضغط "حفظ"

## النسخ الاحتياطي

### تصدير البيانات
1. اذهب إلى "النسخ الاحتياطي"
2. اضغط "تصدير البيانات"
3. سيتم تحميل ملف JSON

### استيراد البيانات
1. اذهب إلى "النسخ الاحتياطي"
2. اضغط "اختيار ملف للاستيراد"
3. اختر ملف JSON
4. اضغط "تأكيد الاستيراد"

## التحويل لتطبيق سطح مكتب

يمكن تحويل النظام لتطبيق سطح مكتب باستخدام Electron:

```bash
npm install electron --save-dev
npm install electron-builder --save-dev
```

## الدعم والمساعدة

### المشاكل الشائعة

**النظام لا يفتح بعد تسجيل الدخول:**
- افتح `test.html` لتشخيص المشكلة
- تأكد من تمكين JavaScript في المتصفح
- امسح cache المتصفح وأعد تحميل الصفحة
- جرب متصفح آخر (Chrome, Firefox, Edge)

**لا تظهر البيانات:**
- تأكد من تمكين JavaScript في المتصفح
- تحقق من عدم حظر localStorage
- امسح cache المتصفح
- أعد تحميل الصفحة بـ Ctrl+F5

**مشاكل الطباعة:**
- تأكد من إعدادات الطابعة
- جرب متصفح آخر
- استخدم معاينة الطباعة أولاً

**فقدان البيانات:**
- استخدم النسخ الاحتياطي المحفوظ
- البيانات محفوظة في localStorage
- تجنب مسح بيانات المتصفح

**رسائل خطأ JavaScript:**
- افتح أدوات المطور (F12)
- تحقق من رسائل الخطأ في Console
- تأكد من سلامة ملفات النظام

## الترخيص

هذا المشروع مجاني ومفتوح المصدر تحت رخصة MIT.

## المطور

**تكنوفلاش**
- الموقع: [technoflash.com](https://technoflash.com)
- البريد: <EMAIL>

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إرسال Pull Request

## الإصدارات

### الإصدار 1.0.0
- الإصدار الأول
- جميع الميزات الأساسية
- واجهة عربية كاملة
- دعم RTL
- نظام طباعة احترافي

---

**شكراً لاستخدام تكنوفلاش! 🚀**
