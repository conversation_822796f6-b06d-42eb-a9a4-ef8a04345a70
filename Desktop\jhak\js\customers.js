// تكنوفلاش - إدارة العملاء
class CustomersManager {
    constructor() {
        this.currentCustomer = null;
        this.filteredCustomers = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    // تحميل صفحة العملاء
    load() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = this.getHTML();
        
        this.loadData();
        this.setupEvents();
    }

    // الحصول على HTML صفحة العملاء
    getHTML() {
        return `
            <div class="customers-page fade-in">
                <div class="page-header">
                    <div class="page-title">
                        <h1><i class="fas fa-users"></i> إدارة العملاء</h1>
                        <p>إدارة بيانات العملاء والأرصدة</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="customersManager.showAddCustomerModal()">
                            <i class="fas fa-plus"></i>
                            إضافة عميل
                        </button>
                        <button class="btn btn-success" onclick="customersManager.exportCustomers()">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                    </div>
                </div>

                <div class="customers-toolbar">
                    <div class="search-section">
                        <div class="search-box">
                            <input type="text" id="customerSearch" placeholder="البحث في العملاء..." 
                                   onkeyup="customersManager.searchCustomers(this.value)">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                    
                    <div class="filter-section">
                        <select id="balanceFilter" onchange="customersManager.filterByBalance(this.value)">
                            <option value="all">جميع العملاء</option>
                            <option value="debtors">المدينين</option>
                            <option value="creditors">الدائنين</option>
                            <option value="zero">رصيد صفر</option>
                        </select>
                    </div>
                </div>

                <div class="customers-stats">
                    <div class="stat-item">
                        <div class="stat-icon customers">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalCustomersCount">٠</h3>
                            <p>إجمالي العملاء</p>
                        </div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-icon debtors">
                            <i class="fas fa-user-minus"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="debtorsCount">٠</h3>
                            <p>العملاء المدينين</p>
                        </div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-icon debts">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalDebts">٠</h3>
                            <p>إجمالي الديون</p>
                        </div>
                    </div>
                </div>

                <div class="customers-table-container">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">قائمة العملاء</h3>
                            <button class="btn btn-sm btn-secondary" onclick="customersManager.refreshCustomers()">
                                <i class="fas fa-refresh"></i>
                                تحديث
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="customersTableContainer">
                                <!-- سيتم تحميل الجدول هنا -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نافذة إضافة/تعديل عميل -->
            <div id="customerModal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="customerModalTitle">إضافة عميل جديد</h3>
                        <button class="modal-close" onclick="customersManager.hideCustomerModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <form id="customerForm" class="modal-body validate-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="customerName">اسم العميل *</label>
                                <input type="text" id="customerName" name="name" 
                                       class="validate-field" data-rules="required|min:2" 
                                       placeholder="أدخل اسم العميل">
                            </div>
                            <div class="form-group">
                                <label for="customerPhone">رقم الهاتف</label>
                                <input type="text" id="customerPhone" name="phone" 
                                       class="validate-field" data-rules="phone" 
                                       placeholder="أدخل رقم الهاتف">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="customerEmail">البريد الإلكتروني</label>
                                <input type="email" id="customerEmail" name="email" 
                                       class="validate-field" data-rules="email" 
                                       placeholder="أدخل البريد الإلكتروني">
                            </div>
                            <div class="form-group">
                                <label for="customerBalance">الرصيد الابتدائي</label>
                                <input type="text" id="customerBalance" name="balance" 
                                       class="validate-field" data-rules="number" 
                                       placeholder="٠.٠٠" value="٠">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="customerAddress">العنوان</label>
                            <textarea id="customerAddress" name="address" 
                                      placeholder="أدخل العنوان (اختياري)"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="customerNotes">ملاحظات</label>
                            <textarea id="customerNotes" name="notes" 
                                      placeholder="ملاحظات إضافية (اختياري)"></textarea>
                        </div>
                    </form>
                    <div class="modal-footer">
                        <button type="submit" form="customerForm" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ
                        </button>
                        <button class="btn btn-secondary" onclick="customersManager.hideCustomerModal()">
                            إلغاء
                        </button>
                    </div>
                </div>
            </div>

            <!-- نافذة كشف حساب العميل -->
            <div id="customerStatementModal" class="modal hidden">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3 id="statementTitle">كشف حساب العميل</h3>
                        <button class="modal-close" onclick="customersManager.hideStatementModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div id="customerStatementContent">
                            <!-- سيتم تحميل كشف الحساب هنا -->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" onclick="customersManager.printStatement()">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                        <button class="btn btn-secondary" onclick="customersManager.hideStatementModal()">
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>

            <!-- نافذة إضافة دفعة -->
            <div id="paymentModal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>إضافة دفعة</h3>
                        <button class="modal-close" onclick="customersManager.hidePaymentModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <form id="paymentForm" class="modal-body validate-form">
                        <div class="form-group">
                            <label for="paymentAmount">المبلغ *</label>
                            <input type="text" id="paymentAmount" name="amount" 
                                   class="validate-field" data-rules="required|number|positive" 
                                   placeholder="أدخل المبلغ">
                        </div>
                        
                        <div class="form-group">
                            <label for="paymentMethod">طريقة الدفع</label>
                            <select id="paymentMethod" name="method">
                                <option value="cash">نقداً</option>
                                <option value="bank">تحويل بنكي</option>
                                <option value="check">شيك</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="paymentNotes">ملاحظات</label>
                            <textarea id="paymentNotes" name="notes" 
                                      placeholder="ملاحظات الدفعة (اختياري)"></textarea>
                        </div>
                    </form>
                    <div class="modal-footer">
                        <button type="submit" form="paymentForm" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ الدفعة
                        </button>
                        <button class="btn btn-secondary" onclick="customersManager.hidePaymentModal()">
                            إلغاء
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        db.on('customerAdded', () => this.refreshCustomers());
        db.on('customerUpdated', () => this.refreshCustomers());
        db.on('customerDeleted', () => this.refreshCustomers());
        db.on('paymentAdded', () => this.refreshCustomers());
    }

    // تحميل البيانات
    loadData() {
        this.loadCustomers();
        this.loadStats();
    }

    // إعداد الأحداث
    setupEvents() {
        const customerForm = document.getElementById('customerForm');
        if (customerForm) {
            customerForm.addEventListener('submit', (e) => this.handleCustomerSubmit(e));
        }

        const paymentForm = document.getElementById('paymentForm');
        if (paymentForm) {
            paymentForm.addEventListener('submit', (e) => this.handlePaymentSubmit(e));
        }
    }

    // تحميل العملاء
    loadCustomers() {
        const customers = db.getCustomers().filter(c => !c.isGuest);
        this.filteredCustomers = [...customers];
        this.displayCustomers();
    }

    // عرض العملاء
    displayCustomers() {
        const container = document.getElementById('customersTableContainer');
        
        if (this.filteredCustomers.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-users"></i>
                    <h3>لا توجد عملاء</h3>
                    <p>ابدأ بإضافة عميل جديد</p>
                    <button class="btn btn-primary" onclick="customersManager.showAddCustomerModal()">
                        <i class="fas fa-plus"></i>
                        إضافة عميل
                    </button>
                </div>
            `;
            return;
        }

        const columns = [
            { key: 'name', title: 'اسم العميل' },
            { key: 'phone', title: 'رقم الهاتف' },
            { key: 'email', title: 'البريد الإلكتروني' },
            { 
                key: 'balance', 
                title: 'الرصيد',
                format: (value) => {
                    const className = value < 0 ? 'negative' : value > 0 ? 'positive' : '';
                    return `<span class="balance ${className}">${app.formatCurrency(value)}</span>`;
                }
            },
            { 
                key: 'createdAt', 
                title: 'تاريخ الإضافة',
                format: (value) => app.formatDate(value)
            },
            {
                key: 'actions',
                title: 'الإجراءات',
                format: (value, row) => `
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-primary" onclick="customersManager.editCustomer('${row.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="customersManager.showStatement('${row.id}')" title="كشف حساب">
                            <i class="fas fa-file-invoice"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="customersManager.showPaymentModal('${row.id}')" title="إضافة دفعة">
                            <i class="fas fa-money-bill"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="customersManager.deleteCustomer('${row.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `
            }
        ];

        appCore.createSortableTable(this.filteredCustomers, columns, 'customersTableContainer');
    }

    // تحميل الإحصائيات
    loadStats() {
        const customers = db.getCustomers().filter(c => !c.isGuest);
        const debtors = customers.filter(c => c.balance < 0);
        const totalDebts = debtors.reduce((sum, c) => sum + Math.abs(c.balance), 0);

        document.getElementById('totalCustomersCount').textContent = db.toArabicNumbers(customers.length);
        document.getElementById('debtorsCount').textContent = db.toArabicNumbers(debtors.length);
        document.getElementById('totalDebts').textContent = app.formatCurrency(totalDebts);
    }

    // البحث في العملاء
    searchCustomers(query) {
        const customers = db.getCustomers().filter(c => !c.isGuest);
        if (!query.trim()) {
            this.filteredCustomers = customers;
        } else {
            const searchTerm = query.toLowerCase();
            this.filteredCustomers = customers.filter(customer =>
                customer.name.toLowerCase().includes(searchTerm) ||
                customer.phone.includes(query) ||
                customer.email.toLowerCase().includes(searchTerm)
            );
        }
        this.displayCustomers();
    }

    // فلترة حسب الرصيد
    filterByBalance(type) {
        const customers = db.getCustomers().filter(c => !c.isGuest);
        switch (type) {
            case 'debtors':
                this.filteredCustomers = customers.filter(c => c.balance < 0);
                break;
            case 'creditors':
                this.filteredCustomers = customers.filter(c => c.balance > 0);
                break;
            case 'zero':
                this.filteredCustomers = customers.filter(c => c.balance === 0);
                break;
            default:
                this.filteredCustomers = customers;
        }
        this.displayCustomers();
    }

    // تحديث العملاء
    refreshCustomers() {
        this.loadData();
        app.showAlert('تم تحديث قائمة العملاء', 'success');
    }

    // عرض نافذة إضافة عميل
    showAddCustomerModal() {
        this.currentCustomer = null;
        document.getElementById('customerModalTitle').textContent = 'إضافة عميل جديد';
        this.resetCustomerForm();
        document.getElementById('customerModal').classList.remove('hidden');
        document.getElementById('customerName').focus();
    }

    // تعديل عميل
    editCustomer(customerId) {
        const customer = db.getCustomerById(customerId);
        if (!customer) {
            app.showAlert('العميل غير موجود', 'error');
            return;
        }

        this.currentCustomer = customer;
        document.getElementById('customerModalTitle').textContent = 'تعديل العميل';
        this.fillCustomerForm(customer);
        document.getElementById('customerModal').classList.remove('hidden');
        document.getElementById('customerName').focus();
    }

    // حذف عميل
    deleteCustomer(customerId) {
        const customer = db.getCustomerById(customerId);
        if (!customer) {
            app.showAlert('العميل غير موجود', 'error');
            return;
        }

        if (customer.isGuest) {
            app.showAlert('لا يمكن حذف العميل الافتراضي', 'warning');
            return;
        }

        app.showConfirm(
            'حذف العميل',
            `هل أنت متأكد من حذف العميل "${customer.name}"؟`,
            () => {
                if (db.deleteCustomer(customerId)) {
                    app.showAlert('تم حذف العميل بنجاح', 'success');
                } else {
                    app.showAlert('حدث خطأ أثناء حذف العميل', 'error');
                }
            }
        );
    }

    // إخفاء نافذة العميل
    hideCustomerModal() {
        document.getElementById('customerModal').classList.add('hidden');
        this.resetCustomerForm();
        this.currentCustomer = null;
    }

    // إعادة تعيين نموذج العميل
    resetCustomerForm() {
        document.getElementById('customerForm').reset();
        document.getElementById('customerBalance').value = '٠';

        document.querySelectorAll('.field-error').forEach(error => error.remove());
        document.querySelectorAll('.error').forEach(field => field.classList.remove('error'));
    }

    // ملء نموذج العميل
    fillCustomerForm(customer) {
        document.getElementById('customerName').value = customer.name;
        document.getElementById('customerPhone').value = customer.phone || '';
        document.getElementById('customerEmail').value = customer.email || '';
        document.getElementById('customerAddress').value = customer.address || '';
        document.getElementById('customerBalance').value = customer.balance || 0;
        document.getElementById('customerNotes').value = customer.notes || '';
    }

    // معالجة إرسال نموذج العميل
    handleCustomerSubmit(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const customerData = {
            name: app.sanitizeText(formData.get('name')),
            phone: formData.get('phone') || '',
            email: formData.get('email') || '',
            address: app.sanitizeText(formData.get('address')) || '',
            balance: app.parseNumber(formData.get('balance')) || 0,
            notes: app.sanitizeText(formData.get('notes')) || ''
        };

        if (this.currentCustomer) {
            if (db.updateCustomer(this.currentCustomer.id, customerData)) {
                app.showAlert('تم تحديث العميل بنجاح', 'success');
                this.hideCustomerModal();
            } else {
                app.showAlert('حدث خطأ أثناء تحديث العميل', 'error');
            }
        } else {
            if (db.addCustomer(customerData)) {
                app.showAlert('تم إضافة العميل بنجاح', 'success');
                this.hideCustomerModal();
            } else {
                app.showAlert('حدث خطأ أثناء إضافة العميل', 'error');
            }
        }
    }

    // عرض كشف حساب العميل
    showStatement(customerId) {
        const customer = db.getCustomerById(customerId);
        if (!customer) {
            app.showAlert('العميل غير موجود', 'error');
            return;
        }

        this.currentCustomer = customer;
        document.getElementById('statementTitle').textContent = `كشف حساب العميل: ${customer.name}`;
        this.loadStatement(customer);
        document.getElementById('customerStatementModal').classList.remove('hidden');
    }

    // تحميل كشف الحساب
    loadStatement(customer) {
        const sales = db.getSales().filter(sale => sale.customerId === customer.id);
        const payments = db.getPayments().filter(payment => payment.customerId === customer.id);

        // دمج المعاملات وترتيبها حسب التاريخ
        const transactions = [];

        sales.forEach(sale => {
            transactions.push({
                type: 'sale',
                date: sale.createdAt,
                description: `فاتورة رقم ${sale.id}`,
                debit: sale.paymentMethod === 'credit' ? sale.total : 0,
                credit: 0,
                balance: 0
            });
        });

        payments.forEach(payment => {
            transactions.push({
                type: 'payment',
                date: payment.createdAt,
                description: `دفعة - ${payment.notes || 'دفعة نقدية'}`,
                debit: 0,
                credit: payment.amount,
                balance: 0
            });
        });

        // ترتيب المعاملات حسب التاريخ
        transactions.sort((a, b) => new Date(a.date) - new Date(b.date));

        // حساب الرصيد التراكمي
        let runningBalance = 0;
        transactions.forEach(transaction => {
            runningBalance += transaction.debit - transaction.credit;
            transaction.balance = runningBalance;
        });

        this.displayStatement(customer, transactions);
    }

    // عرض كشف الحساب
    displayStatement(customer, transactions) {
        const container = document.getElementById('customerStatementContent');

        let content = `
            <div class="statement-header">
                <div class="customer-info">
                    <h3>بيانات العميل</h3>
                    <p><strong>الاسم:</strong> ${customer.name}</p>
                    ${customer.phone ? `<p><strong>الهاتف:</strong> ${customer.phone}</p>` : ''}
                    ${customer.email ? `<p><strong>البريد:</strong> ${customer.email}</p>` : ''}
                    ${customer.address ? `<p><strong>العنوان:</strong> ${customer.address}</p>` : ''}
                </div>

                <div class="company-info">
                    <h3>بيانات الشركة</h3>
                    <p><strong>الاسم:</strong> ${db.getSettings().companyName}</p>
                    <p><strong>الهاتف:</strong> ${db.getSettings().companyPhone}</p>
                    <p><strong>العنوان:</strong> ${db.getSettings().companyAddress}</p>
                </div>
            </div>

            <div class="statement-summary">
                <h2>الرصيد الحالي</h2>
                <div class="balance-amount ${customer.balance < 0 ? 'negative' : customer.balance > 0 ? 'positive' : ''}">
                    ${app.formatCurrency(customer.balance)}
                </div>
            </div>
        `;

        if (transactions.length === 0) {
            content += '<div class="no-transactions">لا توجد معاملات</div>';
        } else {
            content += `
                <div class="transactions-table">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>البيان</th>
                                <th>مدين</th>
                                <th>دائن</th>
                                <th>الرصيد</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            transactions.forEach(transaction => {
                content += `
                    <tr>
                        <td>${app.formatDate(transaction.date)}</td>
                        <td>${transaction.description}</td>
                        <td>${transaction.debit > 0 ? app.formatCurrency(transaction.debit) : '-'}</td>
                        <td>${transaction.credit > 0 ? app.formatCurrency(transaction.credit) : '-'}</td>
                        <td class="${transaction.balance < 0 ? 'negative' : transaction.balance > 0 ? 'positive' : ''}">
                            ${app.formatCurrency(transaction.balance)}
                        </td>
                    </tr>
                `;
            });

            content += `
                        </tbody>
                    </table>
                </div>
            `;
        }

        container.innerHTML = content;
    }

    // إخفاء نافذة كشف الحساب
    hideStatementModal() {
        document.getElementById('customerStatementModal').classList.add('hidden');
        this.currentCustomer = null;
    }

    // طباعة كشف الحساب
    printStatement() {
        if (!this.currentCustomer) return;

        const content = document.getElementById('customerStatementContent').innerHTML;
        app.printContent(content);
    }

    // عرض نافذة إضافة دفعة
    showPaymentModal(customerId) {
        const customer = db.getCustomerById(customerId);
        if (!customer) {
            app.showAlert('العميل غير موجود', 'error');
            return;
        }

        this.currentCustomer = customer;
        this.resetPaymentForm();
        document.getElementById('paymentModal').classList.remove('hidden');
        document.getElementById('paymentAmount').focus();
    }

    // إخفاء نافذة الدفعة
    hidePaymentModal() {
        document.getElementById('paymentModal').classList.add('hidden');
        this.resetPaymentForm();
        this.currentCustomer = null;
    }

    // إعادة تعيين نموذج الدفعة
    resetPaymentForm() {
        document.getElementById('paymentForm').reset();
        document.querySelectorAll('.field-error').forEach(error => error.remove());
        document.querySelectorAll('.error').forEach(field => field.classList.remove('error'));
    }

    // معالجة إرسال نموذج الدفعة
    handlePaymentSubmit(e) {
        e.preventDefault();

        if (!this.currentCustomer) return;

        const formData = new FormData(e.target);
        const paymentData = {
            customerId: this.currentCustomer.id,
            amount: app.parseNumber(formData.get('amount')),
            method: formData.get('method'),
            notes: app.sanitizeText(formData.get('notes')) || ''
        };

        // إضافة الدفعة
        if (db.addPayment(paymentData)) {
            // تحديث رصيد العميل
            const newBalance = this.currentCustomer.balance + paymentData.amount;
            db.updateCustomer(this.currentCustomer.id, { balance: newBalance });

            app.showAlert('تم إضافة الدفعة بنجاح', 'success');
            this.hidePaymentModal();
        } else {
            app.showAlert('حدث خطأ أثناء إضافة الدفعة', 'error');
        }
    }

    // تصدير العملاء
    exportCustomers() {
        const customers = db.getCustomers().filter(c => !c.isGuest);

        const exportData = customers.map(customer => ({
            'اسم العميل': customer.name,
            'رقم الهاتف': customer.phone || '',
            'البريد الإلكتروني': customer.email || '',
            'العنوان': customer.address || '',
            'الرصيد': customer.balance || 0,
            'الملاحظات': customer.notes || '',
            'تاريخ الإضافة': app.formatDate(customer.createdAt)
        }));

        const columns = [
            { key: 'اسم العميل', title: 'اسم العميل' },
            { key: 'رقم الهاتف', title: 'رقم الهاتف' },
            { key: 'البريد الإلكتروني', title: 'البريد الإلكتروني' },
            { key: 'العنوان', title: 'العنوان' },
            { key: 'الرصيد', title: 'الرصيد' },
            { key: 'الملاحظات', title: 'الملاحظات' },
            { key: 'تاريخ الإضافة', title: 'تاريخ الإضافة' }
        ];

        const filename = `العملاء_${new Date().toISOString().split('T')[0]}.csv`;
        appCore.exportToCSV(exportData, filename, columns);

        app.showAlert('تم تصدير العملاء بنجاح', 'success');
    }
}

// إنشاء مثيل إدارة العملاء
const customersManager = new CustomersManager();

// وظيفة تحميل صفحة العملاء
function loadCustomers() {
    if (typeof app !== 'undefined') {
        app.currentPage = 'customers';
        app.updateActiveNavItem('customers');
    }
    customersManager.load();
}
