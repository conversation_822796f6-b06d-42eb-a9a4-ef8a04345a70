// تكنوفلاش - نظام الطباعة
class PrintManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupPrintStyles();
    }

    setupPrintStyles() {
        // إضافة أنماط الطباعة إذا لم تكن موجودة
        if (!document.getElementById('printStyles')) {
            const printStyles = document.createElement('style');
            printStyles.id = 'printStyles';
            printStyles.textContent = `
                @media print {
                    body * {
                        visibility: hidden;
                    }
                    .print-content, .print-content * {
                        visibility: visible;
                    }
                    .print-content {
                        position: absolute;
                        left: 0;
                        top: 0;
                        width: 100%;
                    }
                    .no-print {
                        display: none !important;
                    }
                }
            `;
            document.head.appendChild(printStyles);
        }
    }

    // طباعة فاتورة
    printInvoice(saleData, customer) {
        const settings = db.getSettings();
        
        const invoiceHTML = `
            <div class="print-content invoice">
                <div class="invoice-header">
                    <h1>${settings.companyName}</h1>
                    <div class="company-info">
                        <p>${settings.companyAddress}</p>
                        <p>هاتف: ${settings.companyPhone}</p>
                        <p>بريد إلكتروني: ${settings.companyEmail}</p>
                    </div>
                </div>
                
                <div class="invoice-details">
                    <div class="section">
                        <h3>بيانات الفاتورة</h3>
                        <p><strong>رقم الفاتورة:</strong> ${saleData.id}</p>
                        <p><strong>التاريخ:</strong> ${app.formatDate(saleData.createdAt)}</p>
                        <p><strong>الوقت:</strong> ${app.formatTime(saleData.createdAt)}</p>
                    </div>
                    
                    <div class="section">
                        <h3>بيانات العميل</h3>
                        <p><strong>الاسم:</strong> ${customer.name}</p>
                        ${customer.phone ? `<p><strong>الهاتف:</strong> ${customer.phone}</p>` : ''}
                        ${customer.address ? `<p><strong>العنوان:</strong> ${customer.address}</p>` : ''}
                    </div>
                </div>
                
                <div class="invoice-items">
                    <table>
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${saleData.items.map(item => `
                                <tr>
                                    <td>${item.name}</td>
                                    <td>${app.formatCurrency(item.price)}</td>
                                    <td>${db.toArabicNumbers(item.quantity)} ${item.unit}</td>
                                    <td>${app.formatCurrency(item.price * item.quantity)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                
                <div class="invoice-total">
                    <table>
                        <tr>
                            <td>المجموع الفرعي:</td>
                            <td>${app.formatCurrency(saleData.subtotal)}</td>
                        </tr>
                        ${saleData.discount > 0 ? `
                            <tr>
                                <td>الخصم:</td>
                                <td>-${app.formatCurrency(saleData.discount)}</td>
                            </tr>
                        ` : ''}
                        <tr>
                            <td>الضريبة (${db.toArabicNumbers(settings.taxRate)}%):</td>
                            <td>${app.formatCurrency(saleData.tax)}</td>
                        </tr>
                        <tr class="total-row">
                            <td>الإجمالي:</td>
                            <td>${app.formatCurrency(saleData.total)}</td>
                        </tr>
                    </table>
                </div>
                
                <div class="invoice-footer">
                    <p>طريقة الدفع: ${saleData.paymentMethod === 'cash' ? 'نقداً' : 'آجل'}</p>
                    <p>شكراً لتعاملكم معنا</p>
                </div>
            </div>
        `;

        this.print(invoiceHTML);
    }

    // طباعة إيصال
    printReceipt(saleData, customer) {
        const settings = db.getSettings();
        
        const receiptHTML = `
            <div class="print-content receipt">
                <div class="receipt-header">
                    <h1>${settings.companyName}</h1>
                    <p>${settings.companyPhone}</p>
                    <p>فاتورة رقم: ${saleData.id}</p>
                    <p>${app.formatDate(saleData.createdAt)} ${app.formatTime(saleData.createdAt)}</p>
                </div>
                
                <div class="receipt-items">
                    <table>
                        ${saleData.items.map(item => `
                            <tr>
                                <td>${item.name}</td>
                                <td>${db.toArabicNumbers(item.quantity)}</td>
                                <td>${app.formatCurrency(item.price * item.quantity)}</td>
                            </tr>
                        `).join('')}
                    </table>
                </div>
                
                <div class="receipt-total">
                    <div class="total-line">المجموع: ${app.formatCurrency(saleData.subtotal)}</div>
                    ${saleData.discount > 0 ? `<div class="total-line">الخصم: -${app.formatCurrency(saleData.discount)}</div>` : ''}
                    <div class="total-line">الضريبة: ${app.formatCurrency(saleData.tax)}</div>
                    <div class="total-amount">${app.formatCurrency(saleData.total)}</div>
                </div>
                
                <div class="receipt-footer">
                    <p>العميل: ${customer.name}</p>
                    <p>الدفع: ${saleData.paymentMethod === 'cash' ? 'نقداً' : 'آجل'}</p>
                    <p>شكراً لزيارتكم</p>
                </div>
            </div>
        `;

        this.print(receiptHTML);
    }

    // طباعة كشف حساب عميل
    printCustomerStatement(customer, transactions) {
        const settings = db.getSettings();
        
        const statementHTML = `
            <div class="print-content statement">
                <div class="statement-header">
                    <div class="customer-info">
                        <h3>بيانات العميل</h3>
                        <p><strong>الاسم:</strong> ${customer.name}</p>
                        ${customer.phone ? `<p><strong>الهاتف:</strong> ${customer.phone}</p>` : ''}
                        ${customer.email ? `<p><strong>البريد:</strong> ${customer.email}</p>` : ''}
                        ${customer.address ? `<p><strong>العنوان:</strong> ${customer.address}</p>` : ''}
                    </div>
                    
                    <div class="company-info">
                        <h3>بيانات الشركة</h3>
                        <p><strong>الاسم:</strong> ${settings.companyName}</p>
                        <p><strong>الهاتف:</strong> ${settings.companyPhone}</p>
                        <p><strong>العنوان:</strong> ${settings.companyAddress}</p>
                    </div>
                </div>
                
                <div class="statement-summary">
                    <h2>الرصيد الحالي</h2>
                    <div class="balance-amount ${customer.balance < 0 ? 'negative' : customer.balance > 0 ? 'positive' : ''}">
                        ${app.formatCurrency(customer.balance)}
                    </div>
                </div>
                
                ${transactions.length > 0 ? `
                    <div class="transactions-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>البيان</th>
                                    <th>مدين</th>
                                    <th>دائن</th>
                                    <th>الرصيد</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${transactions.map(transaction => `
                                    <tr>
                                        <td>${app.formatDate(transaction.date)}</td>
                                        <td>${transaction.description}</td>
                                        <td>${transaction.debit > 0 ? app.formatCurrency(transaction.debit) : '-'}</td>
                                        <td>${transaction.credit > 0 ? app.formatCurrency(transaction.credit) : '-'}</td>
                                        <td class="${transaction.balance < 0 ? 'negative' : transaction.balance > 0 ? 'positive' : ''}">
                                            ${app.formatCurrency(transaction.balance)}
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                ` : '<div class="no-transactions">لا توجد معاملات</div>'}
            </div>
        `;

        this.print(statementHTML);
    }

    // طباعة تقرير
    printReport(title, content) {
        const settings = db.getSettings();
        
        const reportHTML = `
            <div class="print-content report">
                <div class="report-header">
                    <h1>${title}</h1>
                    <div class="company-info">
                        <p>${settings.companyName}</p>
                        <p>تاريخ الطباعة: ${app.formatDate(new Date())}</p>
                    </div>
                </div>
                
                <div class="report-content">
                    ${content}
                </div>
            </div>
        `;

        this.print(reportHTML);
    }

    // الطباعة العامة
    print(content) {
        // إنشاء نافذة طباعة جديدة
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        
        const printHTML = `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>طباعة - تكنوفلاش</title>
                <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
                <link rel="stylesheet" href="css/print.css">
                <style>
                    body {
                        font-family: 'Cairo', sans-serif;
                        direction: rtl;
                        margin: 0;
                        padding: 20px;
                        background: white;
                        color: black;
                    }
                    
                    .print-content {
                        max-width: 800px;
                        margin: 0 auto;
                    }
                    
                    @media print {
                        body {
                            padding: 0;
                        }
                        
                        .print-content {
                            max-width: none;
                        }
                    }
                </style>
            </head>
            <body>
                ${content}
                <script>
                    window.onload = function() {
                        setTimeout(function() {
                            window.print();
                            window.close();
                        }, 500);
                    };
                </script>
            </body>
            </html>
        `;

        printWindow.document.write(printHTML);
        printWindow.document.close();
    }

    // معاينة الطباعة
    preview(content) {
        const previewWindow = window.open('', '_blank', 'width=800,height=600');
        
        const previewHTML = `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>معاينة الطباعة - تكنوفلاش</title>
                <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
                <link rel="stylesheet" href="css/print.css">
                <style>
                    body {
                        font-family: 'Cairo', sans-serif;
                        direction: rtl;
                        margin: 0;
                        padding: 20px;
                        background: #f5f5f5;
                    }
                    
                    .preview-container {
                        max-width: 800px;
                        margin: 0 auto;
                        background: white;
                        padding: 20px;
                        box-shadow: 0 0 10px rgba(0,0,0,0.1);
                    }
                    
                    .preview-actions {
                        text-align: center;
                        margin-bottom: 20px;
                        padding: 10px;
                        background: #333;
                        color: white;
                    }
                    
                    .preview-actions button {
                        margin: 0 10px;
                        padding: 10px 20px;
                        background: #007bff;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        cursor: pointer;
                    }
                    
                    .preview-actions button:hover {
                        background: #0056b3;
                    }
                </style>
            </head>
            <body>
                <div class="preview-actions">
                    <button onclick="window.print()">طباعة</button>
                    <button onclick="window.close()">إغلاق</button>
                </div>
                <div class="preview-container">
                    ${content}
                </div>
            </body>
            </html>
        `;

        previewWindow.document.write(previewHTML);
        previewWindow.document.close();
    }
}

// إنشاء مثيل مدير الطباعة
const printManager = new PrintManager();
