// تكنوفلاش - الإعدادات
class SettingsManager {
    constructor() {
        this.init();
    }

    init() {}

    load() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="settings-page fade-in">
                <div class="page-header">
                    <div class="page-title">
                        <h1><i class="fas fa-cog"></i> الإعدادات</h1>
                        <p>إعدادات النظام والشركة</p>
                    </div>
                </div>

                <div class="settings-sections">
                    <!-- إعدادات الشركة -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-building"></i>
                                بيانات الشركة
                            </h3>
                        </div>
                        <div class="card-body">
                            <form id="companyForm" class="validate-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="companyName">اسم الشركة *</label>
                                        <input type="text" id="companyName" name="companyName" 
                                               class="validate-field" data-rules="required|min:2">
                                    </div>
                                    <div class="form-group">
                                        <label for="companyPhone">رقم الهاتف</label>
                                        <input type="text" id="companyPhone" name="companyPhone">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="companyEmail">البريد الإلكتروني</label>
                                        <input type="email" id="companyEmail" name="companyEmail">
                                    </div>
                                    <div class="form-group">
                                        <label for="taxRate">نسبة الضريبة (%)</label>
                                        <input type="text" id="taxRate" name="taxRate" 
                                               class="validate-field" data-rules="number|positive">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="companyAddress">العنوان</label>
                                    <textarea id="companyAddress" name="companyAddress"></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    حفظ بيانات الشركة
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- إعدادات الأمان -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-shield-alt"></i>
                                إعدادات الأمان
                            </h3>
                        </div>
                        <div class="card-body">
                            <form id="securityForm" class="validate-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="currentPassword">كلمة المرور الحالية *</label>
                                        <input type="password" id="currentPassword" name="currentPassword" 
                                               class="validate-field" data-rules="required">
                                    </div>
                                    <div class="form-group">
                                        <label for="newPassword">كلمة المرور الجديدة *</label>
                                        <input type="password" id="newPassword" name="newPassword" 
                                               class="validate-field" data-rules="required|min:3">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="confirmPassword">تأكيد كلمة المرور *</label>
                                    <input type="password" id="confirmPassword" name="confirmPassword" 
                                           class="validate-field" data-rules="required">
                                </div>
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-key"></i>
                                    تغيير كلمة المرور
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- إعدادات النظام -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-desktop"></i>
                                إعدادات النظام
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>تسجيل الدخول</h4>
                                    <p>تفعيل أو إلغاء شاشة تسجيل الدخول</p>
                                </div>
                                <div class="setting-control">
                                    <label class="switch">
                                        <input type="checkbox" id="loginRequired" onchange="settingsManager.toggleLogin(this.checked)">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>الثيم</h4>
                                    <p>اختر المظهر المفضل للنظام</p>
                                </div>
                                <div class="setting-control">
                                    <select id="themeSelect" onchange="settingsManager.changeTheme(this.value)">
                                        <option value="light">مضيء</option>
                                        <option value="dark">داكن</option>
                                    </select>
                                </div>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>تنبيهات المخزون المنخفض</h4>
                                    <p>إظهار تنبيهات عند انخفاض المخزون</p>
                                </div>
                                <div class="setting-control">
                                    <label class="switch">
                                        <input type="checkbox" id="lowStockAlerts" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>تنبيهات الديون</h4>
                                    <p>إظهار تنبيهات للديون المتأخرة</p>
                                </div>
                                <div class="setting-control">
                                    <label class="switch">
                                        <input type="checkbox" id="debtAlerts" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات النظام -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-info-circle"></i>
                                معلومات النظام
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="system-info">
                                <div class="info-item">
                                    <strong>اسم النظام:</strong>
                                    <span>تكنوفلاش - نظام إدارة نقاط البيع العربي</span>
                                </div>
                                <div class="info-item">
                                    <strong>الإصدار:</strong>
                                    <span>١.٠.٠</span>
                                </div>
                                <div class="info-item">
                                    <strong>المطور:</strong>
                                    <span>تكنوفلاش</span>
                                </div>
                                <div class="info-item">
                                    <strong>الترخيص:</strong>
                                    <span>مجاني ومفتوح المصدر</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.loadSettings();
        this.setupEvents();
    }

    loadSettings() {
        const settings = db.getSettings();
        if (!settings) return;

        // تحميل بيانات الشركة
        document.getElementById('companyName').value = settings.companyName || '';
        document.getElementById('companyPhone').value = settings.companyPhone || '';
        document.getElementById('companyEmail').value = settings.companyEmail || '';
        document.getElementById('companyAddress').value = settings.companyAddress || '';
        document.getElementById('taxRate').value = settings.taxRate || 15;

        // تحميل إعدادات النظام
        document.getElementById('themeSelect').value = settings.theme || 'light';
        document.getElementById('lowStockAlerts').checked = settings.notifications?.lowStock !== false;
        document.getElementById('debtAlerts').checked = settings.notifications?.overdueDebts !== false;
    }

    setupEvents() {
        // نموذج بيانات الشركة
        const companyForm = document.getElementById('companyForm');
        if (companyForm) {
            companyForm.addEventListener('submit', (e) => this.handleCompanySubmit(e));
        }

        // نموذج الأمان
        const securityForm = document.getElementById('securityForm');
        if (securityForm) {
            securityForm.addEventListener('submit', (e) => this.handleSecuritySubmit(e));
        }
    }

    handleCompanySubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const settings = db.getSettings() || {};
        
        settings.companyName = app.sanitizeText(formData.get('companyName'));
        settings.companyPhone = formData.get('companyPhone') || '';
        settings.companyEmail = formData.get('companyEmail') || '';
        settings.companyAddress = app.sanitizeText(formData.get('companyAddress')) || '';
        settings.taxRate = app.parseNumber(formData.get('taxRate')) || 15;

        db.saveSettings(settings);
        app.showAlert('تم حفظ بيانات الشركة بنجاح', 'success');
    }

    handleSecuritySubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const currentPassword = formData.get('currentPassword');
        const newPassword = formData.get('newPassword');
        const confirmPassword = formData.get('confirmPassword');
        
        const settings = db.getSettings();
        
        // التحقق من كلمة المرور الحالية
        if (!db.verifyPassword(currentPassword, settings.password)) {
            app.showAlert('كلمة المرور الحالية غير صحيحة', 'error');
            return;
        }
        
        // التحقق من تطابق كلمة المرور الجديدة
        if (newPassword !== confirmPassword) {
            app.showAlert('كلمة المرور الجديدة غير متطابقة', 'error');
            return;
        }
        
        // تحديث كلمة المرور
        settings.password = db.hashPassword(newPassword);
        db.saveSettings(settings);
        
        // إعادة تعيين النموذج
        document.getElementById('securityForm').reset();
        
        app.showAlert('تم تغيير كلمة المرور بنجاح', 'success');
    }

    toggleLogin(required) {
        const settings = db.getSettings() || {};
        settings.loginRequired = required;
        db.saveSettings(settings);

        if (required) {
            app.showAlert('تم تفعيل تسجيل الدخول. سيتم تطبيقه في المرة القادمة', 'info');
        } else {
            app.showAlert('تم إلغاء تسجيل الدخول. سيتم الدخول مباشرة', 'success');
        }
    }

    changeTheme(theme) {
        app.setTheme(theme);

        const settings = db.getSettings() || {};
        settings.theme = theme;
        db.saveSettings(settings);

        app.showAlert('تم تغيير المظهر بنجاح', 'success');
    }
}

const settingsManager = new SettingsManager();

function loadSettings() {
    app.currentPage = 'settings';
    app.updateActiveNavItem('settings');
    settingsManager.load();
}
