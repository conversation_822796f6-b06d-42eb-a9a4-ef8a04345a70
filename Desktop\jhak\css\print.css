/* أنماط الطباعة - تكنوفلاش */

@media print {
    /* إخفاء العناصر غير المطلوبة في الطباعة */
    .sidebar,
    .top-navbar,
    .btn,
    .modal,
    .user-menu,
    .theme-toggle,
    .navbar-controls,
    .card-header .btn,
    .action-buttons,
    .no-print {
        display: none !important;
    }
    
    /* إعدادات الصفحة */
    @page {
        size: A4;
        margin: 1cm;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: #fff;
    }
    
    /* تخطيط الطباعة */
    .main-app {
        display: block;
        grid-template-areas: unset;
        grid-template-columns: unset;
        grid-template-rows: unset;
        height: auto;
    }
    
    .main-content {
        grid-area: unset;
        padding: 0;
        overflow: visible;
    }
    
    .content-area {
        max-width: none;
        margin: 0;
    }
    
    /* البطاقات */
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
        margin-bottom: 20px;
        page-break-inside: avoid;
        background: #fff;
    }
    
    .card-header {
        border-bottom: 2px solid #333;
        margin-bottom: 15px;
        padding-bottom: 10px;
    }
    
    .card-title {
        font-size: 16pt;
        font-weight: bold;
        color: #000;
    }
    
    /* الجداول */
    .table {
        border-collapse: collapse;
        width: 100%;
        font-size: 10pt;
    }
    
    .table th,
    .table td {
        border: 1px solid #333;
        padding: 8px;
        text-align: right;
    }
    
    .table th {
        background: #f0f0f0;
        font-weight: bold;
    }
    
    .table tbody tr:nth-child(even) {
        background: #f9f9f9;
    }
    
    /* الفواتير */
    .invoice-header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 2px solid #333;
        padding-bottom: 20px;
    }
    
    .invoice-header h1 {
        font-size: 24pt;
        margin-bottom: 10px;
        color: #000;
    }
    
    .invoice-header .company-info {
        font-size: 12pt;
        line-height: 1.6;
    }
    
    .invoice-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
    }
    
    .invoice-details .section {
        border: 1px solid #ddd;
        padding: 15px;
        background: #f9f9f9;
    }
    
    .invoice-details .section h3 {
        font-size: 14pt;
        margin-bottom: 10px;
        color: #000;
        border-bottom: 1px solid #ccc;
        padding-bottom: 5px;
    }
    
    .invoice-items {
        margin-bottom: 30px;
    }
    
    .invoice-items table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .invoice-items th {
        background: #333;
        color: #fff;
        padding: 10px;
        text-align: center;
        font-size: 11pt;
    }
    
    .invoice-items td {
        padding: 8px;
        text-align: center;
        border-bottom: 1px solid #ddd;
    }
    
    .invoice-total {
        float: left;
        width: 300px;
        margin-top: 20px;
    }
    
    .invoice-total table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .invoice-total td {
        padding: 8px;
        border: 1px solid #333;
        text-align: right;
    }
    
    .invoice-total .total-row {
        background: #333;
        color: #fff;
        font-weight: bold;
        font-size: 14pt;
    }
    
    /* التقارير */
    .report-header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 2px solid #333;
        padding-bottom: 15px;
    }
    
    .report-header h1 {
        font-size: 20pt;
        margin-bottom: 10px;
        color: #000;
    }
    
    .report-period {
        font-size: 12pt;
        color: #666;
        margin-bottom: 10px;
    }
    
    .report-summary {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .summary-card {
        border: 1px solid #ddd;
        padding: 15px;
        text-align: center;
        background: #f9f9f9;
    }
    
    .summary-card h3 {
        font-size: 14pt;
        margin-bottom: 10px;
        color: #000;
    }
    
    .summary-card .value {
        font-size: 18pt;
        font-weight: bold;
        color: #333;
    }
    
    /* كشف الحساب */
    .statement-header {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #333;
    }
    
    .statement-header .customer-info,
    .statement-header .company-info {
        border: 1px solid #ddd;
        padding: 15px;
        background: #f9f9f9;
    }
    
    .statement-header h3 {
        font-size: 14pt;
        margin-bottom: 10px;
        color: #000;
        border-bottom: 1px solid #ccc;
        padding-bottom: 5px;
    }
    
    .statement-summary {
        margin-bottom: 30px;
        text-align: center;
        padding: 20px;
        border: 2px solid #333;
        background: #f0f0f0;
    }
    
    .statement-summary h2 {
        font-size: 18pt;
        margin-bottom: 15px;
        color: #000;
    }
    
    .balance-amount {
        font-size: 24pt;
        font-weight: bold;
        color: #d32f2f;
    }
    
    .balance-amount.positive {
        color: #2e7d32;
    }
    
    /* الإيصالات */
    .receipt {
        width: 80mm;
        margin: 0 auto;
        font-size: 10pt;
        line-height: 1.3;
    }
    
    .receipt-header {
        text-align: center;
        margin-bottom: 15px;
        border-bottom: 1px dashed #333;
        padding-bottom: 10px;
    }
    
    .receipt-header h1 {
        font-size: 14pt;
        margin-bottom: 5px;
    }
    
    .receipt-items {
        margin-bottom: 15px;
    }
    
    .receipt-items table {
        width: 100%;
        font-size: 9pt;
    }
    
    .receipt-items td {
        padding: 2px 0;
        border-bottom: 1px dotted #ccc;
    }
    
    .receipt-total {
        border-top: 1px dashed #333;
        padding-top: 10px;
        text-align: center;
    }
    
    .receipt-total .total-amount {
        font-size: 14pt;
        font-weight: bold;
        margin: 10px 0;
    }
    
    .receipt-footer {
        text-align: center;
        margin-top: 15px;
        border-top: 1px dashed #333;
        padding-top: 10px;
        font-size: 8pt;
    }
    
    /* تحسينات عامة للطباعة */
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
        color: #000;
    }
    
    table {
        page-break-inside: avoid;
    }
    
    tr {
        page-break-inside: avoid;
    }
    
    .page-break {
        page-break-before: always;
    }
    
    .no-page-break {
        page-break-inside: avoid;
    }
    
    /* الأرقام العربية */
    .arabic-numbers {
        font-family: 'Cairo', sans-serif;
    }
    
    /* تنسيق العملة */
    .currency {
        font-weight: bold;
    }
    
    .currency::after {
        content: " ريال";
        font-weight: normal;
        font-size: 0.9em;
    }
    
    /* تنسيق التاريخ */
    .date {
        direction: rtl;
        text-align: right;
    }
    
    /* الهوامش والمسافات */
    .print-margin-top {
        margin-top: 20px;
    }
    
    .print-margin-bottom {
        margin-bottom: 20px;
    }
    
    .print-padding {
        padding: 15px;
    }
    
    /* خط فاصل */
    .print-divider {
        border-top: 1px solid #333;
        margin: 20px 0;
    }
    
    .print-divider.dashed {
        border-top: 1px dashed #333;
    }
    
    .print-divider.thick {
        border-top: 2px solid #333;
    }
}
