// تكنوفلاش - النسخ الاحتياطي
class BackupManager {
    constructor() {
        this.init();
    }

    init() {}

    load() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="backup-page fade-in">
                <div class="page-header">
                    <div class="page-title">
                        <h1><i class="fas fa-database"></i> النسخ الاحتياطي</h1>
                        <p>إدارة النسخ الاحتياطية للبيانات</p>
                    </div>
                </div>

                <div class="backup-sections">
                    <!-- تصدير البيانات -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-download"></i>
                                تصدير البيانات
                            </h3>
                        </div>
                        <div class="card-body">
                            <p>قم بتصدير جميع بيانات النظام كنسخة احتياطية</p>
                            <div class="backup-actions">
                                <button class="btn btn-primary" onclick="backupManager.exportData()">
                                    <i class="fas fa-download"></i>
                                    تصدير البيانات
                                </button>
                                <button class="btn btn-secondary" onclick="backupManager.exportSettings()">
                                    <i class="fas fa-cog"></i>
                                    تصدير الإعدادات فقط
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- استيراد البيانات -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-upload"></i>
                                استيراد البيانات
                            </h3>
                        </div>
                        <div class="card-body">
                            <p>استيراد البيانات من ملف نسخة احتياطية</p>
                            <div class="import-section">
                                <input type="file" id="importFile" accept=".json" style="display: none;" 
                                       onchange="backupManager.handleFileSelect(this)">
                                <button class="btn btn-success" onclick="document.getElementById('importFile').click()">
                                    <i class="fas fa-upload"></i>
                                    اختيار ملف للاستيراد
                                </button>
                                <div id="importStatus" class="import-status hidden"></div>
                            </div>
                        </div>
                    </div>

                    <!-- النسخ التلقائي -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-clock"></i>
                                النسخ التلقائي
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="auto-backup-info">
                                <div class="info-item">
                                    <strong>حالة النسخ التلقائي:</strong>
                                    <span class="status active">نشط</span>
                                </div>
                                <div class="info-item">
                                    <strong>تكرار النسخ:</strong>
                                    <span>كل ٥ دقائق</span>
                                </div>
                                <div class="info-item">
                                    <strong>آخر نسخة احتياطية:</strong>
                                    <span id="lastBackupTime">-</span>
                                </div>
                            </div>
                            <button class="btn btn-warning" onclick="backupManager.createManualBackup()">
                                <i class="fas fa-save"></i>
                                إنشاء نسخة احتياطية الآن
                            </button>
                        </div>
                    </div>

                    <!-- إحصائيات البيانات -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-pie"></i>
                                إحصائيات البيانات
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="data-stats">
                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-box"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h4 id="productsCount">٠</h4>
                                        <p>منتج</p>
                                    </div>
                                </div>
                                
                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h4 id="customersCount">٠</h4>
                                        <p>عميل</p>
                                    </div>
                                </div>
                                
                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-truck"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h4 id="suppliersCount">٠</h4>
                                        <p>مورد</p>
                                    </div>
                                </div>
                                
                                <div class="stat-item">
                                    <div class="stat-icon">
                                        <i class="fas fa-receipt"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h4 id="salesCount">٠</h4>
                                        <p>فاتورة</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.loadStats();
        this.loadLastBackupTime();
    }

    loadStats() {
        const products = db.getProducts();
        const customers = db.getCustomers().filter(c => !c.isGuest);
        const suppliers = db.getSuppliers();
        const sales = db.getSales();
        
        document.getElementById('productsCount').textContent = db.toArabicNumbers(products.length);
        document.getElementById('customersCount').textContent = db.toArabicNumbers(customers.length);
        document.getElementById('suppliersCount').textContent = db.toArabicNumbers(suppliers.length);
        document.getElementById('salesCount').textContent = db.toArabicNumbers(sales.length);
    }

    loadLastBackupTime() {
        const lastBackup = localStorage.getItem('technoflash_pos_backup');
        if (lastBackup) {
            try {
                const backup = JSON.parse(lastBackup);
                const backupTime = new Date(backup.timestamp);
                document.getElementById('lastBackupTime').textContent = 
                    `${app.formatDate(backupTime)} ${app.formatTime(backupTime)}`;
            } catch (error) {
                document.getElementById('lastBackupTime').textContent = 'غير متوفر';
            }
        } else {
            document.getElementById('lastBackupTime').textContent = 'لم يتم إنشاء نسخة احتياطية بعد';
        }
    }

    exportData() {
        const backup = db.exportData();
        const dataStr = JSON.stringify(backup, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `technoflash_backup_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        app.showAlert('تم تصدير البيانات بنجاح', 'success');
    }

    exportSettings() {
        const settings = db.getSettings();
        const dataStr = JSON.stringify({ settings }, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `technoflash_settings_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        app.showAlert('تم تصدير الإعدادات بنجاح', 'success');
    }

    handleFileSelect(input) {
        const file = input.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);
                this.showImportPreview(data);
            } catch (error) {
                app.showAlert('ملف غير صالح', 'error');
            }
        };
        reader.readAsText(file);
    }

    showImportPreview(data) {
        const statusDiv = document.getElementById('importStatus');
        statusDiv.classList.remove('hidden');
        
        let preview = '<h4>معاينة البيانات المراد استيرادها:</h4><ul>';
        
        if (data.data) {
            Object.keys(data.data).forEach(table => {
                const count = Array.isArray(data.data[table]) ? data.data[table].length : 0;
                preview += `<li>${table}: ${db.toArabicNumbers(count)} عنصر</li>`;
            });
        }
        
        preview += '</ul>';
        preview += `
            <div class="import-actions">
                <button class="btn btn-danger" onclick="backupManager.confirmImport()">
                    <i class="fas fa-upload"></i>
                    تأكيد الاستيراد
                </button>
                <button class="btn btn-secondary" onclick="backupManager.cancelImport()">
                    إلغاء
                </button>
            </div>
        `;
        
        statusDiv.innerHTML = preview;
        this.importData = data;
    }

    confirmImport() {
        if (!this.importData) return;
        
        app.showConfirm(
            'تأكيد الاستيراد',
            'سيتم استبدال جميع البيانات الحالية. هل أنت متأكد؟',
            () => {
                if (db.importData(this.importData)) {
                    app.showAlert('تم استيراد البيانات بنجاح', 'success');
                    this.cancelImport();
                    this.loadStats();
                    
                    // إعادة تحميل الصفحة لتحديث البيانات
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    app.showAlert('حدث خطأ أثناء استيراد البيانات', 'error');
                }
            }
        );
    }

    cancelImport() {
        document.getElementById('importStatus').classList.add('hidden');
        document.getElementById('importFile').value = '';
        this.importData = null;
    }

    createManualBackup() {
        const backup = db.createBackup();
        if (backup) {
            this.loadLastBackupTime();
            app.showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
        } else {
            app.showAlert('حدث خطأ أثناء إنشاء النسخة الاحتياطية', 'error');
        }
    }
}

const backupManager = new BackupManager();

function loadBackup() {
    if (typeof app !== 'undefined') {
        app.currentPage = 'backup';
        app.updateActiveNavItem('backup');
    }
    backupManager.load();
}
