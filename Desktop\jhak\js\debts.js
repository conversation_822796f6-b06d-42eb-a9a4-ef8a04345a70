// تكنوفلاش - إدارة الديون والمدفوعات
class DebtsManager {
    constructor() {
        this.init();
    }

    init() {}

    load() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="debts-page fade-in">
                <div class="page-header">
                    <div class="page-title">
                        <h1><i class="fas fa-money-bill-wave"></i> إدارة الديون والمدفوعات</h1>
                        <p>متابعة الديون وتسجيل المدفوعات</p>
                    </div>
                </div>
                
                <div class="debts-stats">
                    <div class="stat-item">
                        <div class="stat-icon debtors">
                            <i class="fas fa-user-minus"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalDebtors">٠</h3>
                            <p>العملاء المدينين</p>
                        </div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-icon debts">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalDebtsAmount">٠</h3>
                            <p>إجمالي الديون</p>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">العملاء المدينين</h3>
                    </div>
                    <div class="card-body">
                        <div id="debtorsTableContainer"></div>
                    </div>
                </div>
            </div>
        `;
        
        this.loadData();
    }

    loadData() {
        const debtors = db.getDebtorCustomers();
        const totalDebts = db.getTotalDebts();
        
        document.getElementById('totalDebtors').textContent = db.toArabicNumbers(debtors.length);
        document.getElementById('totalDebtsAmount').textContent = app.formatCurrency(totalDebts);
        
        this.displayDebtors(debtors);
    }

    displayDebtors(debtors) {
        const container = document.getElementById('debtorsTableContainer');
        
        if (debtors.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-check-circle"></i>
                    <h3>لا توجد ديون</h3>
                    <p>جميع العملاء مسددين</p>
                </div>
            `;
            return;
        }

        const columns = [
            { key: 'name', title: 'اسم العميل' },
            { key: 'phone', title: 'رقم الهاتف' },
            { 
                key: 'balance', 
                title: 'المبلغ المستحق',
                format: (value) => `<span class="debt-amount">${app.formatCurrency(Math.abs(value))}</span>`
            },
            {
                key: 'actions',
                title: 'الإجراءات',
                format: (value, row) => `
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-success" onclick="customersManager.showPaymentModal('${row.id}')" title="إضافة دفعة">
                            <i class="fas fa-money-bill"></i>
                            دفعة
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="customersManager.showStatement('${row.id}')" title="كشف حساب">
                            <i class="fas fa-file-invoice"></i>
                            كشف حساب
                        </button>
                    </div>
                `
            }
        ];

        appCore.createSortableTable(debtors, columns, 'debtorsTableContainer');
    }
}

const debtsManager = new DebtsManager();

function loadDebts() {
    if (typeof app !== 'undefined') {
        app.currentPage = 'debts';
        app.updateActiveNavItem('debts');
    }
    debtsManager.load();
}
